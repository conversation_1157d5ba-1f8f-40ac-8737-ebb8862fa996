import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { ArrowLeft, Eye, EyeOff, CheckCircle, Home } from "lucide-react";
import { Link, useNavigate } from "react-router-dom";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export const ResetPasswordPage = () => {
  const navigate = useNavigate();
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    const checkAuthSession = async () => {
      try {
        // Check if we have an active session
        const { data: { session }, error } = await supabase.auth.getSession();

        console.log('Reset password page - checking session:', {
          hasSession: !!session,
          sessionError: error?.message
        });

        if (error) {
          console.error('Error getting session:', error);
          toast.error('Invalid reset link. Please request a new password reset.');
          navigate('/forgot-password');
          return;
        }

        if (!session) {
          console.log('No active session found, redirecting to forgot-password');
          toast.error('Invalid reset link. Please request a new password reset.');
          navigate('/forgot-password');
          return;
        }

        console.log('Valid session found, user can reset password');
      } catch (err) {
        console.error('Error checking auth session:', err);
        toast.error('Invalid reset link. Please request a new password reset.');
        navigate('/forgot-password');
      }
    };

    checkAuthSession();
  }, [navigate]);

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {};

    if (!password) {
      newErrors.password = 'Password is required';
    } else if (password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters';
    }

    if (!confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (password !== confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await supabase.auth.updateUser({
        password: password
      });

      if (error) {
        toast.error(error.message);
        setErrors({ general: error.message });
      } else {
        setIsSuccess(true);
        toast.success('Password updated successfully!');
      }
    } catch (err: any) {
      console.error("Reset password error:", err);
      setErrors({ general: "An unexpected error occurred" });
      toast.error("Failed to update password");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (isSuccess) {
    return (
      <div className="min-h-screen bg-[#111111] text-gray-300 relative">
        {/* Subtle Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0" style={{
            backgroundImage: 'radial-gradient(circle at 25% 25%, #8B5CF6 0%, transparent 50%), radial-gradient(circle at 75% 75%, #8B5CF6 0%, transparent 50%)',
            backgroundSize: '400px 400px'
          }} />
        </div>

        {/* Back To Home Button */}
        <div className="absolute top-6 left-6 z-50">
          <Link to="/">
            <Button
              variant="ghost"
              size="sm"
              className="bg-[#1a1a1a] border border-gray-700 text-gray-300 hover:bg-[#2a2a2a] hover:text-white hover:border-purple-400 transition-all duration-200"
            >
              <Home className="w-4 h-4 mr-2" />
              Back To Home
            </Button>
          </Link>
        </div>

        <div className="flex items-center justify-center min-h-screen p-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.4 }}
            className="w-full max-w-md bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 text-center space-y-6"
          >
            <div className="w-16 h-16 bg-green-500/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <CheckCircle className="w-8 h-8 text-green-400" />
            </div>
            <h1 className="text-2xl font-semibold text-white mb-2">Password Updated!</h1>
            <p className="text-gray-400 text-sm">
              Your password has been successfully updated. You can now sign in with your new password.
            </p>
            <Link to="/sign-in">
              <Button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-medium h-11 rounded-lg transition-colors duration-200">
                Sign In
              </Button>
            </Link>
          </motion.div>
        </div>

        {/* Footer */}
        <div className="absolute bottom-6 left-6 right-6 text-center">
          <p className="text-gray-500 text-xs">
            © 2024 Pegasus Tools. All rights reserved.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 25% 25%, #8B5CF6 0%, transparent 50%), radial-gradient(circle at 75% 75%, #8B5CF6 0%, transparent 50%)',
          backgroundSize: '400px 400px'
        }} />
      </div>

      {/* Back To Home Button */}
      <div className="absolute top-6 left-6 z-50">
        <Link to="/">
          <Button
            variant="ghost"
            size="sm"
            className="bg-[#1a1a1a] border border-gray-700 text-gray-300 hover:bg-[#2a2a2a] hover:text-white hover:border-purple-400 transition-all duration-200"
          >
            <Home className="w-4 h-4 mr-2" />
            Back To Home
          </Button>
        </Link>
      </div>

      <div className="flex items-center justify-center min-h-screen p-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
          className="w-full max-w-md bg-[#1a1a1a] border border-gray-700 rounded-xl p-8 space-y-6"
        >
          <div className="text-center">
            <h1 className="text-2xl font-semibold text-white mb-2">Set New Password</h1>
            <p className="text-gray-400 text-sm">
              Enter your new password below.
            </p>
          </div>

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm text-gray-300 font-medium">
                New Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11 pr-12"
                  placeholder="Enter new password (min. 6 characters)"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors duration-200"
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.password && (
                <p className="text-red-400 text-sm mt-1">{errors.password}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm text-gray-300 font-medium">
                Confirm New Password
              </Label>
              <div className="relative">
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="bg-[#111111] border border-gray-700 text-white placeholder:text-gray-500 focus:border-purple-400 focus:ring-1 focus:ring-purple-400/20 transition-colors duration-200 h-11 pr-12"
                  placeholder="Confirm new password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors duration-200"
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {errors.confirmPassword && (
                <p className="text-red-400 text-sm mt-1">{errors.confirmPassword}</p>
              )}
            </div>

            {errors.general && (
              <div className="bg-red-500/10 border border-red-500/30 rounded-lg p-3">
                <p className="text-red-400 text-sm text-center">{errors.general}</p>
              </div>
            )}

            <Button
              type="submit"
              disabled={isSubmitting || !password || !confirmPassword}
              className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-purple-600/50 text-white font-medium h-11 rounded-lg transition-colors duration-200"
            >
              {isSubmitting ? "Updating..." : "Update Password"}
            </Button>
          </form>

          <div className="text-center">
            <Link
              to="/sign-in"
              className="text-gray-400 hover:text-purple-400 text-sm hover:underline inline-flex items-center transition-colors duration-200"
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              Back to Sign In
            </Link>
          </div>
        </motion.div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-6 left-6 right-6 text-center">
        <p className="text-gray-500 text-xs">
        <span>© {new Date().getFullYear()} Pegasus Tools. All rights reserved.</span>  
        </p>
      </div>
    </div>
  );
};

export default ResetPasswordPage;
