
import React from 'react';
import { motion } from 'framer-motion';
import {
  Search, Book, FileQuestion, ArrowRight, Award, BookOpen, Video,
  Download, Users, Star, TrendingUp, Clock, Shield, Zap, HelpCircle,
  FileText, PlayCircle, Bookmark, ExternalLink, ChevronRight
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';

const KnowledgeBase = () => {
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Function to handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const searchQuery = formData.get('search') as string;

    if (searchQuery.trim()) {
      toast.info(`Searching for: ${searchQuery}`);
    } else {
      toast.warning('Please enter a search query');
    }
  };

  // Function to handle article clicks
  const handleArticleClick = (title: string) => {
    toast.info(`Opening article: ${title}`);
  };

  // Function to handle category clicks
  const handleCategoryClick = (category: string) => {
    toast.info(`Viewing ${category} category`);
  };

  // Knowledge base stats
  const kbStats = [
    { icon: BookOpen, value: "200+", label: "Articles", color: "text-blue-400" },
    { icon: Video, value: "50+", label: "Video Tutorials", color: "text-red-400" },
    { icon: Download, value: "1M+", label: "Downloads", color: "text-green-400" },
    { icon: Users, value: "10K+", label: "Community Members", color: "text-purple-400" },
  ];

  // Featured categories with enhanced data
  const categories = [
    {
      title: 'Getting Started',
      icon: Book,
      color: 'from-blue-500 to-blue-600',
      description: 'Essential guides for new users',
      articleCount: 25,
      difficulty: 'Beginner'
    },
    {
      title: 'Troubleshooting',
      icon: FileQuestion,
      color: 'from-red-500 to-red-600',
      description: 'Solutions to common problems',
      articleCount: 45,
      difficulty: 'Intermediate'
    },
    {
      title: 'Advanced Features',
      icon: Award,
      color: 'from-purple-500 to-purple-600',
      description: 'Master advanced techniques',
      articleCount: 30,
      difficulty: 'Advanced'
    },
    {
      title: 'Device Compatibility',
      icon: Shield,
      color: 'from-green-500 to-green-600',
      description: 'Supported devices and models',
      articleCount: 40,
      difficulty: 'All Levels'
    }
  ];

  // Popular articles with enhanced data
  const popularArticles = [
    {
      title: "Complete Guide to Flashing Firmware with Pegasus Tool",
      category: "Getting Started",
      readTime: "15 min read",
      views: "25K views",
      difficulty: "Beginner",
      lastUpdated: "2 days ago"
    },
    {
      title: "Troubleshooting Device Connection Issues",
      category: "Troubleshooting",
      readTime: "8 min read",
      views: "18K views",
      difficulty: "Intermediate",
      lastUpdated: "1 week ago"
    },
    {
      title: "Understanding Security Levels and Bootloader Unlocking",
      category: "Advanced Features",
      readTime: "12 min read",
      views: "15K views",
      difficulty: "Advanced",
      lastUpdated: "3 days ago"
    },
    {
      title: "How to Update Pegasus Tool to Latest Version",
      category: "Getting Started",
      readTime: "5 min read",
      views: "22K views",
      difficulty: "Beginner",
      lastUpdated: "1 day ago"
    },
    {
      title: "Backup and Restore Device Data Safely",
      category: "Advanced Features",
      readTime: "10 min read",
      views: "12K views",
      difficulty: "Intermediate",
      lastUpdated: "5 days ago"
    },
    {
      title: "Working with Locked Bootloaders - Best Practices",
      category: "Advanced Features",
      readTime: "20 min read",
      views: "8K views",
      difficulty: "Advanced",
      lastUpdated: "1 week ago"
    }
  ];

  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Animated particles */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-blue-500/5"
          style={{
            width: Math.random() * 80 + 40,
            height: Math.random() * 80 + 40,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -150, 0],
            x: [0, Math.random() * 80 - 40, 0],
            opacity: [0, 0.3, 0],
          }}
          transition={{
            duration: Math.random() * 25 + 20,
            repeat: Infinity,
            delay: Math.random() * 15,
          }}
        />
      ))}

      <div className="relative z-10 pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="text-center mb-16"
          >
            <motion.div variants={itemVariants} className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-blue-400 px-4 py-1 rounded-full text-sm font-medium">
                Knowledge Base
              </span>
            </motion.div>

            <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-white mb-4">
              Learn & <span className="text-blue-400">Master</span> Pegasus Tool
            </motion.h1>

            <motion.p variants={itemVariants} className="text-lg text-gray-400 max-w-3xl mx-auto mb-10">
              Comprehensive guides, tutorials, and documentation to help you become a Pegasus Tool expert
            </motion.p>

            {/* Search section */}
            <motion.form variants={itemVariants} onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="relative flex items-center">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  name="search"
                  type="text"
                  placeholder="Search articles, guides, tutorials, and documentation..."
                  className="pl-12 pr-20 py-4 w-full text-base bg-[#1a1a1a] border border-gray-700 rounded-full text-white placeholder:text-gray-400 focus:border-blue-400 focus:outline-none"
                />
                <Button
                  type="submit"
                  className="absolute right-2 bg-blue-600 hover:bg-blue-700 text-white rounded-full px-6 py-2 transition-all duration-300 hover:scale-105"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </motion.form>
          </motion.div>

          {/* Knowledge Base Stats */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {kbStats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="text-center"
                  >
                    <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-2">
                      <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-blue-900/30 to-blue-800/20 rounded-full mb-4 mx-auto">
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                      <h3 className="text-xl font-bold text-white mb-1">{stat.value}</h3>
                      <p className="text-gray-400 text-sm">{stat.label}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Featured Categories */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Browse by <span className="text-blue-400">Category</span>
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Find exactly what you're looking for with our organized knowledge categories
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {categories.map((category, index) => {
                const Icon = category.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="group cursor-pointer"
                    onClick={() => handleCategoryClick(category.title)}
                  >
                    <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-2 h-full">
                      <div className={`w-16 h-16 flex items-center justify-center bg-gradient-to-br ${category.color} rounded-full mb-6 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>

                      <h3 className="text-xl font-semibold text-white mb-2 text-center">{category.title}</h3>
                      <p className="text-gray-400 text-center mb-4">{category.description}</p>

                      <div className="flex items-center justify-between text-sm mb-4">
                        <span className="bg-gray-800/50 text-blue-400 px-3 py-1 rounded-full">
                          {category.articleCount} articles
                        </span>
                        <span className="text-gray-500">
                          {category.difficulty}
                        </span>
                      </div>

                      <Button
                        variant="ghost"
                        className="text-blue-400 hover:bg-blue-400/10 group-hover:text-blue-300 w-full justify-between"
                        onClick={() => handleCategoryClick(category.title)}
                      >
                        Explore
                        <ChevronRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Popular Articles */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Popular <span className="text-blue-400">Articles</span>
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Most viewed and helpful articles from our knowledge base
              </p>
            </motion.div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {popularArticles.map((article, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="group cursor-pointer"
                  onClick={() => handleArticleClick(article.title)}
                >
                  <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-blue-400/50 transition-all duration-300 hover:-translate-y-1">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="font-semibold text-white mb-2 group-hover:text-blue-400 transition-colors">
                          {article.title}
                        </h3>
                        <div className="flex items-center gap-4 text-sm text-gray-400 mb-3">
                          <span className="bg-gray-800/50 text-blue-400 px-2 py-1 rounded">
                            {article.category}
                          </span>
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {article.readTime}
                          </span>
                          <span className="flex items-center gap-1">
                            <TrendingUp className="w-3 h-3" />
                            {article.views}
                          </span>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className={`text-xs px-2 py-1 rounded-full ${
                            article.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-400' :
                            article.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-400' :
                            'bg-red-500/20 text-red-400'
                          }`}>
                            {article.difficulty}
                          </span>
                          <span className="text-xs text-gray-500">
                            Updated {article.lastUpdated}
                          </span>
                        </div>
                      </div>
                      <Button variant="ghost" size="icon" className="group shrink-0 ml-4">
                        <ArrowRight className="h-5 w-5 transition-transform group-hover:translate-x-1 text-blue-400" />
                      </Button>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>

            <motion.div variants={itemVariants} className="mt-8 text-center">
              <Button
                variant="outline"
                className="border-blue-400 text-blue-400 hover:bg-blue-400/10"
                onClick={() => toast.info('Loading more articles')}
              >
                View All Articles
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Contact Support */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
          >
            <div className="bg-gradient-to-r from-blue-600/20 via-blue-500/10 to-blue-700/20 rounded-2xl p-8 border border-gray-700/50 backdrop-blur-lg relative overflow-hidden">
              <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

              <div className="relative z-10 text-center">
                <motion.div variants={itemVariants}>
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    Can't find what you're <span className="text-blue-400">looking for?</span>
                  </h2>
                  <p className="text-gray-400 mb-8 max-w-2xl mx-auto">
                    Our expert support team is ready to help you with any questions or technical challenges
                  </p>

                  <div className="flex flex-col sm:flex-row gap-4 justify-center">
                    <Button
                      className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-3 transition-all duration-300 hover:scale-105"
                      onClick={() => toast.success('Support request initiated')}
                    >
                      <HelpCircle className="w-4 h-4 mr-2" />
                      Contact Support
                    </Button>
                    <Button
                      variant="outline"
                      className="border-blue-400 text-blue-400 hover:bg-blue-400/10 px-8 py-3"
                      onClick={() => toast.info('Opening community forum')}
                    >
                      <Users className="w-4 h-4 mr-2" />
                      Join Community
                    </Button>
                  </div>
                </motion.div>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default KnowledgeBase;
