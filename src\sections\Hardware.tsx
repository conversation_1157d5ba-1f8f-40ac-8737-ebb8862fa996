

import HardwareHero from '@/components/hardware/HardwareHero';
import CircuitDiagrams from '@/components/hardware/CircuitDiagrams';
import CircuitComponents from '@/components/hardware/CircuitComponents';
import SupportedModels from '@/sections/SupportedModels';
import Pricing from '@/sections/Pricing';
import ThemedSection from '@/components/layout/ThemedSection';
import ThemedHeader from '@/components/layout/ThemedHeader';

const Hardware = () => {
  return (
    <div className="hardware-page">
      {/* Hardware Hero Section */}
      <HardwareHero />

      {/* Circuit Diagrams Section */}
      <CircuitDiagrams />

      {/* Circuit Components Section */}
      <CircuitComponents />

      {/* Supported Models Section */}
      <ThemedSection
        theme="hardware"
        id="hardware-supported-models"
        variant="primary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="hardware"
          title="Supported Device Models"
          subtitle="Comprehensive hardware compatibility for professional repair and diagnostics"
          highlightWord="Device"
          size="normal"
        />
        <SupportedModels theme="hardware" />
      </ThemedSection>

      {/* Pricing Section */}
      <ThemedSection
        theme="hardware"
        id="hardware-pricing"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="hardware"
          title="Hardware Solutions Pricing"
          subtitle="Professional hardware documentation and repair guides with competitive pricing"
          highlightWord="Pricing"
          size="normal"
        />
        <Pricing theme="hardware" />
      </ThemedSection>
    </div>
  );
};

export default Hardware;
