import { SimpleSignInForm } from "./SimpleSignInForm";
import { Link } from "react-router-dom";
import { Home, Shield, Smartphone, Zap } from "lucide-react";
import { Button } from "@/components/ui/button";

export const SignInPage = () => {
  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative">
      {/* Subtle Background Pattern */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute inset-0" style={{
          backgroundImage: 'radial-gradient(circle at 25% 25%, #8B5CF6 0%, transparent 50%), radial-gradient(circle at 75% 75%, #8B5CF6 0%, transparent 50%)',
          backgroundSize: '400px 400px'
        }} />
      </div>

      {/* Back To Home Button */}
      <div className="absolute top-6 left-6 z-50">
        <Link to="/">
          <Button
            variant="ghost"
            size="sm"
            className="bg-[#1a1a1a] border border-gray-700 text-gray-300 hover:bg-[#2a2a2a] hover:text-white hover:border-purple-400 transition-all duration-200"
          >
            <Home className="w-4 h-4 mr-2" />
            Back To Home
          </Button>
        </Link>
      </div>

      <div className="flex min-h-screen">
        {/* Main Content - Centered Form */}
        <div className="flex-1 flex items-center justify-center p-6 relative z-10">
          <div className="w-full max-w-md">
            <SimpleSignInForm />
          </div>
        </div>

        {/* Right Section - Simplified Feature Display */}
        <div className="hidden lg:flex lg:w-96 xl:w-[28rem] bg-[#1a1a1a] border-l border-gray-700 items-center justify-center p-8">
          <div className="max-w-sm">
            <div className="text-center mb-8">
              <h2 className="text-2xl font-semibold text-white mb-3">
                Welcome to <span className="text-purple-400">Pegasus Tool</span>
              </h2>
              <p className="text-gray-400 text-sm">
                Professional smartphone repair and unlocking system
              </p>
            </div>

            <div className="space-y-4">
              {[
                {
                  icon: Shield,
                  title: "Secure Authentication",
                  description: "Advanced security protocols"
                },
                {
                  icon: Smartphone,
                  title: "Device Support",
                  description: "All major smartphone brands"
                },
                {
                  icon: Zap,
                  title: "Fast Processing",
                  description: "Lightning-fast operations"
                }
              ].map((feature, index) => (
                <div
                  key={index}
                  className="flex items-start space-x-3 p-4 rounded-lg bg-[#111111] border border-gray-700 hover:border-purple-400/50 transition-colors duration-200"
                >
                  <div className="w-8 h-8 flex-shrink-0 rounded-full bg-purple-400/20 flex items-center justify-center">
                    <feature.icon className="h-4 w-4 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-sm font-medium text-white mb-1">{feature.title}</h3>
                    <p className="text-xs text-gray-400">{feature.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="absolute bottom-6 left-6 right-6 text-center">
        <p className="text-gray-500 text-xs">
        <span>© {new Date().getFullYear()} Pegasus Tools. All rights reserved.</span>
        </p>
      </div>
    </div>
  );
};

export default SignInPage;
