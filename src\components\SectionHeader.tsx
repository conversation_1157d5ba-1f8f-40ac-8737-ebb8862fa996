
import React from "react";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { getTheme, ThemeType } from "@/styles/design-system";

interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  centered?: boolean;
  titleClassName?: string;
  subtitleClassName?: string;
  containerClassName?: string;
  highlightWord?: string;
  theme?: ThemeType;
  size?: 'small' | 'normal' | 'large';
  withIcon?: React.ReactNode;
  withUnderline?: boolean;
  animate?: boolean;
}

const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  centered = true,
  titleClassName = "",
  subtitleClassName = "",
  containerClassName = "",
  highlightWord,
  theme = 'neutral',
  size = 'normal',
  withIcon,
  withUnderline = true,
  animate = true
}) => {
  const themeConfig = getTheme(theme);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          title: 'text-2xl md:text-3xl',
          subtitle: 'text-base md:text-lg',
          spacing: 'mb-8'
        };
      case 'large':
        return {
          title: 'text-4xl md:text-5xl lg:text-6xl',
          subtitle: 'text-xl md:text-2xl',
          spacing: 'mb-16'
        };
      default:
        return {
          title: 'text-3xl md:text-4xl lg:text-5xl',
          subtitle: 'text-lg md:text-xl',
          spacing: 'mb-12'
        };
    }
  };

  const sizeClasses = getSizeClasses();
  // Split title to highlight specific word if provided
  const renderTitle = () => {
    if (!highlightWord || !title.includes(highlightWord)) {
      return <span>{title}</span>;
    }

    const parts = title.split(highlightWord);
    const highlightColor = theme === 'hardware' ? 'text-pegasus-blue-600 dark:text-pegasus-blue-400' :
                          theme === 'software' ? 'text-pegasus-orange' :
                          'text-gray-600 dark:text-gray-400';

    const underlineColor = theme === 'hardware' ? 'bg-pegasus-blue-600/30' :
                          theme === 'software' ? 'bg-pegasus-orange/30' :
                          'bg-gray-600/30';

    return (
      <>
        {parts[0]}
        <span className={cn("relative inline-block", highlightColor)}>
          {highlightWord}
          <span className={cn("absolute bottom-0 left-0 w-full h-1 rounded-full", underlineColor)}></span>
        </span>
        {parts[1]}
      </>
    );
  };

  const getAlignClass = () => {
    return centered ? 'text-center' : 'text-left';
  };

  const headerContent = (
    <div className={cn(
      sizeClasses.spacing,
      getAlignClass(),
      containerClassName
    )}>
      {/* Icon */}
      {withIcon && (
        <motion.div
          className={cn(
            'inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-6',
            theme === 'hardware' ? 'bg-pegasus-blue-100 dark:bg-pegasus-blue-900/30' :
            theme === 'software' ? 'bg-orange-100 dark:bg-orange-900/30' :
            'bg-gray-100 dark:bg-gray-800/30',
            themeConfig.primary
          )}
          initial={{ scale: 0, rotate: -180 }}
          whileInView={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {withIcon}
        </motion.div>
      )}

      <motion.h2
        className={cn(
          sizeClasses.title,
          "font-bold mb-4",
          centered ? "mx-auto" : "",
          "text-gray-800 dark:text-gray-100",
          titleClassName
        )}
        initial={animate ? { opacity: 0, y: 20 } : {}}
        whileInView={animate ? { opacity: 1, y: 0 } : {}}
        viewport={{ once: true }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {renderTitle()}
      </motion.h2>

      {subtitle && (
        <motion.p
          className={cn(
            sizeClasses.subtitle,
            "text-gray-600 dark:text-gray-300 max-w-3xl",
            centered ? "mx-auto" : "",
            subtitleClassName
          )}
          initial={animate ? { opacity: 0, y: 20 } : {}}
          whileInView={animate ? { opacity: 1, y: 0 } : {}}
          viewport={{ once: true }}
          transition={{ duration: 0.6, delay: animate ? 0.2 : 0, ease: "easeOut" }}
        >
          {subtitle}
        </motion.p>
      )}

      {/* Decorative Underline */}
      {withUnderline && (
        <motion.div
          className={cn(
            'h-1 rounded-full mt-6',
            centered ? 'w-24 mx-auto' : 'w-24',
            theme === 'hardware' ? 'bg-gradient-to-r from-pegasus-blue-400 to-pegasus-blue-600' :
            theme === 'software' ? 'bg-gradient-to-r from-orange-400 to-orange-600' :
            'bg-gradient-to-r from-gray-400 to-gray-600'
          )}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 0.8, delay: animate ? 0.4 : 0 }}
          viewport={{ once: true }}
        />
      )}
    </div>
  );

  return headerContent;
};

export default SectionHeader;
