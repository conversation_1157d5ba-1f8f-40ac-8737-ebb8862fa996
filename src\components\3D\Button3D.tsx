
import React, { useState } from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';

interface Button3DProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode;
  variant?: 'default' | 'glass' | 'solid' | 'gradient' | 'outline';
  depth?: 'none' | 'low' | 'medium' | 'high';
  size?: 'sm' | 'md' | 'lg' | 'xl';
}

const Button3D: React.FC<Button3DProps> = ({
  children,
  className,
  variant = 'default',
  depth = 'medium',
  size = 'md',
  ...props
}) => {
  const [isPressed, setIsPressed] = useState(false);

  const variantClasses = {
    default: 'bg-pegasus-orange hover:bg-pegasus-orange-600 text-white',
    glass: 'bg-white/50 dark:bg-gray-800/50 backdrop-blur-md border border-white/20 dark:border-gray-700/30 text-gray-800 dark:text-white',
    solid: 'bg-gray-800 dark:bg-white text-white dark:text-gray-800',
    gradient: 'bg-gradient-to-r from-pegasus-orange to-pegasus-orange-600 text-white',
    outline: 'bg-transparent border-2 border-pegasus-orange text-pegasus-orange hover:bg-pegasus-orange/10',
  };

  const depthClasses = {
    none: '',
    low: 'before:h-1',
    medium: 'before:h-2',
    high: 'before:h-3',
  };
  
  const sizeClasses = {
    sm: 'text-xs py-1 px-3',
    md: 'text-sm py-2 px-4',
    lg: 'text-base py-2.5 px-5',
    xl: 'text-lg py-3 px-6',
  };

  return (
    <div className={cn(
      'relative group inline-block',
      depthClasses[depth],
      className
    )}>
      {/* 3D base/shadow element */}
      <span 
        className={cn(
          'absolute inset-0 rounded-md bg-black/20 dark:bg-black/40 translate-y-[4px] group-hover:translate-y-[2px] transition-transform',
          isPressed && 'translate-y-[1px]'
        )}
      />
      
      {/* Main button */}
      <button
        className={cn(
          'relative rounded-md flex items-center justify-center font-medium transition-all transform-gpu shadow-lg',
          variantClasses[variant],
          sizeClasses[size],
          isPressed ? 'translate-y-[3px]' : 'translate-y-0 group-hover:translate-y-[2px]'
        )}
        onMouseDown={() => setIsPressed(true)}
        onMouseUp={() => setIsPressed(false)}
        onMouseLeave={() => setIsPressed(false)}
        onTouchStart={() => setIsPressed(true)}
        onTouchEnd={() => setIsPressed(false)}
        {...props}
      >
        {children}
      </button>
    </div>
  );
};

export default Button3D;
