import { ForgotPasswordForm } from "./ForgotPasswordForm";
import { motion } from "framer-motion";
import { <PERSON> } from "react-router-dom";
import { Home, Mail, Shield, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";

export const ForgotPasswordPage = () => {
  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
      {/* Animated Background Particles */}
      {[...Array(5)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-purple-400/10"
          style={{
            width: Math.random() * 40 + 20,
            height: Math.random() * 40 + 20,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -80, 0],
            x: [0, Math.random() * 40 - 20, 0],
            opacity: [0, 0.3, 0],
          }}
          transition={{
            duration: Math.random() * 8 + 8,
            repeat: Infinity,
            delay: Math.random() * 4,
          }}
        />
      ))}

      {/* Back To Home Button */}
      <motion.div
        className="absolute top-6 left-6 z-50"
        initial={{ opacity: 0, x: -20 }}
        animate={{ opacity: 1, x: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Link to="/">
          <Button
            variant="ghost"
            size="sm"
            className="bg-[#1a1a1a] border border-gray-700/50 text-gray-300 hover:bg-gray-700/50 hover:text-white hover:border-purple-400/50 transition-all duration-300 group backdrop-blur-lg"
          >
            <Home className="w-4 h-4 mr-2 group-hover:scale-110 transition-transform" />
            Back To Home
          </Button>
        </Link>
      </motion.div>

      <div className="flex flex-col lg:flex-row min-h-screen">
        {/* Left Section - Form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8 relative z-10">
          <ForgotPasswordForm />
        </div>

        {/* Right Section - Feature Display */}
        <div className="w-full lg:w-1/2 relative overflow-hidden min-h-[50vh] lg:min-h-screen flex items-center justify-center">
          {/* Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-purple-900/20 via-purple-800/10 to-purple-700/20" />

          {/* Feature Cards */}
          <div className="relative z-10 p-8 flex flex-col justify-center h-full">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center mb-12"
            >
              <h2 className="text-3xl md:text-4xl font-semibold text-white mb-4">
                Reset Your <span className="text-purple-400">Password</span>
              </h2>
              <p className="text-gray-400 text-lg max-w-md mx-auto">
                Secure password recovery for your Pegasus Tool account
              </p>
            </motion.div>

            <div className="space-y-6 max-w-md mx-auto">
              {[
                {
                  icon: Mail,
                  title: "Email Verification",
                  description: "We'll send a secure reset link to your email address"
                },
                {
                  icon: Shield,
                  title: "Secure Process",
                  description: "Advanced security protocols protect your account"
                },
                {
                  icon: Clock,
                  title: "Quick Recovery",
                  description: "Reset your password and get back to work in minutes"
                }
              ].map((feature, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.6, delay: 0.4 + index * 0.1 }}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 flex-shrink-0 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center">
                      <feature.icon className="h-6 w-6 text-purple-400" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                      <p className="text-gray-400 text-sm">{feature.description}</p>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Footer */}
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6, delay: 0.8 }}
        className="absolute bottom-8 left-8 right-8 text-center"
      >
        <p className="text-gray-500 text-sm">
          © 2024 Pegasus Tools. All rights reserved.
        </p>
      </motion.div>
    </div>
  );
};

export default ForgotPasswordPage;
