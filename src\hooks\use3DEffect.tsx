
import { useEffect, useRef } from 'react';

interface Use3DEffectOptions {
  intensity?: number;
  perspective?: number;
  resetOnExit?: boolean;
  glare?: boolean;
  scale?: boolean;
}

export const use3DEffect = ({
  intensity = 15,
  perspective = 1000,
  resetOnExit = true,
  glare = false,
  scale = false
}: Use3DEffectOptions = {}) => {
  const elementRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    const element = elementRef.current;
    if (!element) return;

    element.style.transformStyle = 'preserve-3d';
    element.style.perspective = `${perspective}px`;
    element.style.transition = 'transform 0.1s ease';

    // Create glare element if enabled
    let glareElement: HTMLDivElement | null = null;
    if (glare) {
      glareElement = document.createElement('div');
      glareElement.className = 'absolute inset-0 bg-gradient-to-tr from-transparent via-gray-200/0 to-gray-200/20 opacity-0 transition-opacity duration-300 pointer-events-none';
      element.style.position = 'relative';
      element.style.overflow = 'hidden';
      element.appendChild(glareElement);
    }

    const handleMouseMove = (e: MouseEvent) => {
      const rect = element.getBoundingClientRect();

      // Calculate mouse position relative to element center
      const centerX = rect.left + rect.width / 2;
      const centerY = rect.top + rect.height / 2;
      const mouseX = e.clientX - centerX;
      const mouseY = e.clientY - centerY;

      // Calculate rotation based on mouse position
      const rotateX = (mouseY / (rect.height / 2)) * -intensity;
      const rotateY = (mouseX / (rect.width / 2)) * intensity;

      // Apply transformation
      element.style.transform = `rotateX(${rotateX}deg) rotateY(${rotateY}deg) ${scale ? 'scale(1.05)' : ''}`;

      // Update glare position if enabled
      if (glare && glareElement) {
        const percentX = (e.clientX - rect.left) / rect.width;
        const percentY = (e.clientY - rect.top) / rect.height;

        glareElement.style.opacity = '1';
        glareElement.style.background = `radial-gradient(circle at ${percentX * 100}% ${percentY * 100}%, rgba(255,255,255,0.25) 0%, transparent 80%)`;
      }
    };

    const handleMouseLeave = () => {
      if (resetOnExit) {
        element.style.transform = scale ? 'scale(1)' : 'none';
      }

      // Reset glare if enabled
      if (glare && glareElement) {
        glareElement.style.opacity = '0';
      }
    };

    // Add event listeners
    element.addEventListener('mousemove', handleMouseMove);
    element.addEventListener('mouseleave', handleMouseLeave);

    return () => {
      // Clean up event listeners
      element.removeEventListener('mousemove', handleMouseMove);
      element.removeEventListener('mouseleave', handleMouseLeave);

      // Remove glare element if created
      if (glare && glareElement && element.contains(glareElement)) {
        element.removeChild(glareElement);
      }
    };
  }, [intensity, perspective, resetOnExit, glare, scale]);

  return elementRef;
};
