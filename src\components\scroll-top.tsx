
import React, { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { ArrowUp } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

export const ScrollTop = () => {
  const [showButton, setShowButton] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      // Show the button when scrolled down 300px
      if (window.scrollY > 300) {
        setShowButton(true);
      } else {
        setShowButton(false);
      }
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: "smooth"
    });
  };

  return (
    <AnimatePresence>
      {showButton && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.3 }}
        >
      <Button
        variant="outline"
        size="icon"
        className="fixed bottom-6 left-6 z-50 p-5 bg-purple-600 text-white border-none rounded-full shadow-2xl hover:bg-purple-700 hover:scale-110 hover:shadow-purple-400/50 transition-all duration-300"
        onClick={scrollToTop}
        aria-label="Scroll to top"
      >
        <ArrowUp className="h-8 w-8" />
      </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
};
