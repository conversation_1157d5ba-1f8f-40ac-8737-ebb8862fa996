
import { useEffect, useRef, useState } from 'react';

interface ParallaxOptions {
  speed?: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  layer?: number; // Used for z-index ordering
  easing?: string;
}

/**
 * A hook to create parallax scrolling effects on elements
 */
export const useParallax = ({
  speed = 0.2,
  direction = 'up',
  layer = 1,
  easing = 'cubic-bezier(0.25, 0.1, 0.25, 1.0)'
}: ParallaxOptions = {}) => {
  const elementRef = useRef<HTMLDivElement>(null);
  const [offset, setOffset] = useState(0);
  
  useEffect(() => {
    const handleScroll = () => {
      if (!elementRef.current) return;
      
      // Calculate how far the element is from the top of the viewport
      const rect = elementRef.current.getBoundingClientRect();
      const elementTop = rect.top;
      const elementHeight = rect.height;
      const windowHeight = window.innerHeight;
      
      // Only animate when element is in view
      if (
        elementTop < windowHeight &&
        elementTop + elementHeight > 0
      ) {
        // Calculate parallax offset based on the element's position in viewport
        const scrollPos = window.scrollY;
        const viewportMiddle = windowHeight / 2;
        const elementMiddle = elementTop + elementHeight / 2;
        const distanceFromMiddle = elementMiddle - viewportMiddle;
        
        // Apply parallax effect with the specified speed
        const parallaxOffset = distanceFromMiddle * speed;
        
        // Set the calculated offset
        setOffset(parallaxOffset);
      }
    };
    
    // Set initial position
    handleScroll();
    
    // Add scroll listener
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    // Clean up
    return () => window.removeEventListener('scroll', handleScroll);
  }, [speed, direction]);
  
  // Calculate transform style based on direction
  const getTransform = () => {
    switch (direction) {
      case 'up':
        return `translateY(${-offset}px)`;
      case 'down':
        return `translateY(${offset}px)`;
      case 'left':
        return `translateX(${-offset}px)`;
      case 'right':
        return `translateX(${offset}px)`;
      default:
        return `translateY(${-offset}px)`;
    }
  };
  
  const style = {
    transform: getTransform(),
    transition: `transform 0.1s ${easing}`,
    willChange: 'transform',
    zIndex: layer,
  };
  
  return { ref: elementRef, style };
};
