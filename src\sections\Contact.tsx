
import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { But<PERSON> } from '@/components/ui/button';
import { toast } from 'sonner';
import StatisticsStyleHeader from '@/components/layout/StatisticsStyleHeader';
import { Mail, Phone, Clock, SendIcon } from 'lucide-react';

const Contact = () => {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    toast.success('Message sent successfully! We\'ll get back to you soon.');
  };

  return (
    <section className="py-20 bg-[#111111] text-gray-300 relative overflow-hidden">
      <div className="absolute inset-0 z-1 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      <div className="container mx-auto px-4 relative z-10">
        <StatisticsStyleHeader
          badge="Contact Us"
          title="Get in touch with our support team or sales representatives"
          highlightWord="Contact"
          subtitle="We're here to help you with any questions or technical support you need"
        />

        <div className="flex flex-col lg:flex-row gap-10 max-w-6xl mx-auto">
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.1 }}
            viewport={{ once: true }}
            whileHover={{
              y: -4,
              scale: 1.01,
              transition: { duration: 0.3 }
            }}
          >
            <Card className="p-8 h-full bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/20">
              <h2 className="text-2xl font-bold mb-6 font-montserrat text-white">Send us a message</h2>

              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-2">
                    <Label htmlFor="name" className="text-gray-300">Name</Label>
                    <Input
                      id="name"
                      placeholder="Your name"
                      required
                      className="transition-all duration-200 focus:border-[#C084FC] focus:ring-1 focus:ring-[#C084FC]"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="email" className="text-gray-300">Email</Label>
                    <Input
                      id="email"
                      type="email"
                      placeholder="<EMAIL>"
                      required
                      className="transition-all duration-200 focus:border-[#C084FC] focus:ring-1 focus:ring-[#C084FC]"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="subject" className="text-gray-300">Subject</Label>
                  <Input
                    id="subject"
                    placeholder="How can we help you?"
                    required
                    className="transition-all duration-200 focus:border-[#C084FC] focus:ring-1 focus:ring-[#C084FC]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message" className="text-gray-300">Message</Label>
                  <Textarea
                    id="message"
                    placeholder="Type your message here..."
                    rows={5}
                    required
                    className="transition-all duration-200 focus:border-[#C084FC] focus:ring-1 focus:ring-[#C084FC]"
                  />
                </div>

                <Button
                  type="submit"
                  className="w-full bg-[#C084FC] hover:bg-[#C084FC]/80 text-white font-semibold py-3 rounded-md shadow-md hover:shadow-lg transition-all duration-300 flex items-center justify-center gap-2"
                >
                  <SendIcon className="h-5 w-5" /> Send Message
                </Button>
              </form>
            </Card>
          </motion.div>

          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            viewport={{ once: true }}
            whileHover={{
              y: -4,
              scale: 1.01,
              transition: { duration: 0.3 }
            }}
          >
            <Card className="p-8 h-full bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 hover:shadow-2xl hover:shadow-purple-500/20">
              <h2 className="text-2xl font-bold mb-6 font-montserrat text-white">Contact Information</h2>

              <div className="space-y-8">
                <div className="flex items-start gap-5">
                  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center flex-shrink-0 shadow-md">
                    <Mail className="h-7 w-7 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Email</h3>
                    <p className="text-gray-400 mb-1">General Inquiries:</p>
                    <p className="text-purple-400 font-medium mb-3 hover:underline cursor-pointer"><EMAIL></p>
                    <p className="text-gray-400 mb-1">Technical Support:</p>
                    <p className="text-purple-400 font-medium hover:underline cursor-pointer"><EMAIL></p>
                  </div>
                </div>

                <div className="flex items-start gap-5">
                  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center flex-shrink-0 shadow-md">
                    <Phone className="h-7 w-7 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Phone</h3>
                    <p className="text-gray-400 mb-1">Sales Department:</p>
                    <p className="text-purple-400 font-medium mb-3 hover:underline cursor-pointer">+****************</p>
                    <p className="text-gray-400 mb-1">Technical Support:</p>
                    <p className="text-purple-400 font-medium hover:underline cursor-pointer">+****************</p>
                  </div>
                </div>

                <div className="flex items-start gap-5">
                  <div className="w-14 h-14 rounded-full bg-gradient-to-br from-purple-900/30 to-purple-800/20 flex items-center justify-center flex-shrink-0 shadow-md">
                    <Clock className="h-7 w-7 text-purple-400" />
                  </div>
                  <div>
                    <h3 className="text-xl font-semibold mb-2 text-white">Business Hours</h3>
                    <div className="grid grid-cols-2 gap-x-2 gap-y-1">
                      <p className="text-gray-400">Monday - Friday:</p>
                      <p className="text-purple-400 font-medium">9:00 AM - 6:00 PM (UTC)</p>
                      <p className="text-gray-400">Saturday:</p>
                      <p className="text-purple-400 font-medium">10:00 AM - 2:00 PM (UTC)</p>
                      <p className="text-gray-400">Sunday:</p>
                      <p className="text-purple-400 font-medium">Closed</p>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default Contact;
