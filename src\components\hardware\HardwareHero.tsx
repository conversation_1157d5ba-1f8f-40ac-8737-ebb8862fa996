
import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { supabase } from '@/integrations/supabase/client';
import { toast } from 'sonner';
import { Download, ArrowRight, CircuitBoard } from 'lucide-react';
import { use3DEffect } from '@/hooks/use3DEffect';

const HardwareHero = () => {
  const hardwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });

  const [hardwareImageUrl, setHardwareImageUrl] = useState("/lovable-uploads/hardware-diagram.png");
  const [latestDiagram, setLatestDiagram] = useState<{
    version: string,
    name: string | null,
    link: string | null
  } | null>(null);

  useEffect(() => {
    // Fetch hardware diagram image and link
    const fetchHardwareData = async () => {
      try {
        // Get hardware image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHardwareImageUrl(imageData.publicUrl);
        }

        // For now, let's use a mock diagram data since the hardware_diagrams table doesn't exist
        setLatestDiagram({
          version: "1.0",
          name: "Circuit Diagram",
          link: "/lovable-uploads/hardware-diagram.png"
        });

        // When the hardware_diagrams table exists, you can uncomment this code:
        /*
        const { data, error } = await supabase
          .from('hardware_diagrams')
          .select('version, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestDiagram(data[0]);
        }
        */
      } catch (error) {
        console.error('Error fetching hardware data:', error);
      }
    };

    fetchHardwareData();
  }, []);

  const handleDownload = () => {
    if (latestDiagram?.link) {
      window.location.href = latestDiagram.link;
      toast.success('Download started!');
    } else {
      toast.info("Download link is not available at the moment. Please try again later.");
    }
  };

  return (
    <section className="pt-28 pb-20 bg-gradient-to-bl from-slate-900 via-pegasus-blue-900/90 to-pegasus-blue-700/80 dark:from-gray-900 dark:via-slate-800/95 dark:to-pegasus-blue-900/70 overflow-hidden relative">
      {/* Enhanced background pattern */}
      <div className="absolute inset-0 bg-[url('/patterns/dots.svg')] opacity-10"></div>
      <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

      {/* Enhanced animated background elements */}
      <motion.div
        className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-r from-pegasus-blue-500/15 to-pegasus-blue-400/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.4, 0.6, 0.4],
          rotate: [0, 180, 360]
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      ></motion.div>
      <motion.div
        className="absolute bottom-10 left-10 w-60 h-60 bg-gradient-to-r from-pegasus-blue-500/15 to-pegasus-blue-400/10 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.4, 1],
          opacity: [0.3, 0.5, 0.3],
          rotate: [360, 180, 0]
        }}
        transition={{
          duration: 15,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      ></motion.div>
      <motion.div
        className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-pegasus-blue-400/5 to-pegasus-blue-300/5 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.2, 1],
          opacity: [0.2, 0.4, 0.2]
        }}
        transition={{
          duration: 20,
          repeat: Infinity,
          repeatType: "reverse"
        }}
      ></motion.div>

      <div className="container mx-auto px-4 relative">
        <div className="flex flex-col md:flex-row items-center">
          <motion.div
            className="md:w-1/2 mb-10 md:mb-0"
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-slate-200 font-montserrat tracking-tight leading-tight">
              Pegasus <span className="bg-gradient-to-r from-pegasus-blue-400 to-pegasus-blue-300 bg-clip-text text-transparent">Hardware</span>
            </h1>

            <motion.p
              className="text-lg md:text-xl text-slate-200 mb-8 max-w-lg leading-relaxed"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3, duration: 0.7 }}
              style={{
                textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
              }}
            >
              Comprehensive <span className="text-pegasus-blue-300 font-semibold">hardware documentation</span> and technical resources for mobile device repair. Access detailed <span className="text-pegasus-blue-300 font-semibold">circuit schematics, component layouts</span>, and professional repair guides for precise diagnostics and component-level repairs.
            </motion.p>

            <motion.div
              className="space-y-4 md:space-y-0 md:space-x-4 flex flex-col md:flex-row"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5, duration: 0.7 }}
            >
              <Button
                className="bg-gradient-to-r from-pegasus-blue-500 to-pegasus-blue-600 hover:from-pegasus-blue-600 hover:to-pegasus-blue-700 text-white px-8 py-6 rounded-2xl text-lg w-full md:w-auto transition-all duration-500 hover:-translate-y-2 shadow-xl hover:shadow-2xl flex items-center justify-center group border border-pegasus-blue-400/20 backdrop-blur-sm"
                onClick={handleDownload}
                style={{
                  boxShadow: '0 10px 25px rgba(59, 130, 246, 0.3), 0 0 0 1px rgba(59, 130, 246, 0.1)'
                }}
              >
                <Download className="mr-3 h-6 w-6 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" />
                <span className="relative font-semibold">
                  Access Hardware Resources {latestDiagram && `v${latestDiagram.version}`}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white/40 scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                </span>
              </Button>

              <Button
                  className="bg-transparent border-2 border-pegasus-blue-400/60 hover:border-pegasus-blue-300 text-white px-8 py-6 rounded-2xl text-lg w-full md:w-auto transition-all duration-500 hover:-translate-y-2 shadow-xl hover:shadow-2xl hover:bg-gradient-to-r hover:from-pegasus-blue-500/20 hover:to-pegasus-blue-400/20 flex items-center justify-center group backdrop-blur-sm"
                  onClick={() => document.getElementById('circuit-diagrams')?.scrollIntoView({ behavior: 'smooth' })}
                style={{
                  boxShadow: '0 10px 25px rgba(59, 130, 246, 0.2), 0 0 0 1px rgba(59, 130, 246, 0.1)'
                }}
              >
                <ArrowRight className="mr-3 h-6 w-6 group-hover:scale-125 group-hover:translate-x-1 transition-all duration-300" />
                <span className="relative font-semibold">
                  Browse Components
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-pegasus-blue-300/40 scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                </span>
              </Button>
            </motion.div>
          </motion.div>

          <motion.div
            className="md:w-1/2 relative z-10"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.7, ease: "easeOut" }}
          >
            <div className="relative flex items-center justify-center">
              {/* Enhanced glowing background circle */}
              <motion.div
                className="bg-gradient-to-br from-pegasus-blue-500/25 via-pegasus-blue-400/20 to-pegasus-blue-700/15 rounded-full h-72 w-72 md:h-[420px] md:w-[420px] mx-auto absolute"
                animate={{
                  scale: [1, 1.08, 1],
                  opacity: [0.6, 0.9, 0.6],
                  rotate: [0, 360]
                }}
                transition={{
                  duration: 8,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              ></motion.div>

              {/* Secondary glow effect */}
              <motion.div
                className="bg-gradient-to-br from-pegasus-blue-400/15 to-pegasus-blue-300/10 rounded-full h-80 w-80 md:h-[480px] md:w-[480px] mx-auto absolute blur-xl"
                animate={{
                  scale: [1, 1.15, 1],
                  opacity: [0.3, 0.6, 0.3]
                }}
                transition={{
                  duration: 12,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
              ></motion.div>

              <motion.div
                ref={hardwareImageRef}
                className="relative z-10 max-w-full md:max-w-md mx-auto bg-gradient-to-br from-slate-800/40 via-slate-700/30 to-slate-900/40 backdrop-blur-sm rounded-2xl p-6 preserve-3d border border-pegasus-blue-500/20 shadow-2xl"
                animate={{
                  y: [0, -12, 0],
                }}
                transition={{
                  duration: 7,
                  repeat: Infinity,
                  repeatType: "reverse"
                }}
                style={{
                  maxHeight: '500px',
                  boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(59, 130, 246, 0.1)'
                }}
              >
                <img
                  src={hardwareImageUrl}
                  alt="Professional Hardware Circuit Diagrams and Component Layouts"
                  className="w-full h-full object-contain rounded-lg"
                  style={{
                    filter: 'drop-shadow(0 15px 25px rgba(0, 0, 0, 0.6)) brightness(1.1) contrast(1.05)'
                  }}
                  onError={(e) => {
                    const target = e.target;
                    if (target && target instanceof HTMLImageElement) {
                      target.src = "/lovable-uploads/hardware-diagram.png";
                    }
                  }}
                />

                {/* Enhanced glowing circuit points */}
                <motion.div
                  className="absolute top-1/4 left-1/4 w-4 h-4 bg-pegasus-blue-400 rounded-full shadow-lg"
                  animate={{
                    scale: [1, 1.3, 1],
                    opacity: [0.7, 1, 0.7],
                    boxShadow: ['0 0 10px rgba(59, 130, 246, 0.5)', '0 0 20px rgba(59, 130, 246, 0.8)', '0 0 10px rgba(59, 130, 246, 0.5)']
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>
                <motion.div
                  className="absolute top-1/2 left-1/3 w-3 h-3 bg-pegasus-blue-300 rounded-full shadow-lg"
                  animate={{
                    scale: [1, 1.4, 1],
                    opacity: [0.6, 1, 0.6],
                    boxShadow: ['0 0 8px rgba(147, 197, 253, 0.5)', '0 0 16px rgba(147, 197, 253, 0.8)', '0 0 8px rgba(147, 197, 253, 0.5)']
                  }}
                  transition={{
                    duration: 2.5,
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 0.5
                  }}
                ></motion.div>
                <motion.div
                  className="absolute bottom-1/3 right-1/4 w-4 h-4 bg-pegasus-blue-200 rounded-full shadow-lg"
                  animate={{
                    scale: [1, 1.2, 1],
                    opacity: [0.8, 1, 0.8],
                    boxShadow: ['0 0 12px rgba(191, 219, 254, 0.5)', '0 0 24px rgba(191, 219, 254, 0.8)', '0 0 12px rgba(191, 219, 254, 0.5)']
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    repeatType: "reverse",
                    delay: 1
                  }}
                ></motion.div>
              </motion.div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  );
};

export default HardwareHero;
