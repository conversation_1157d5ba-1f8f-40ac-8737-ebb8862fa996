import { useState, useEffect } from "react";
import { motion, AnimatePresence } from "framer-motion";

interface Feature {
  id: number;
  text: string;
}

const features: Feature[] = [
  { id: 1, text: "Advanced Analytics Dashboard" },
  { id: 2, text: "Real-time Collaboration Tools" },
  { id: 3, text: "AI-Powered Insights & Intelligence" },
  { id: 4, text: "Secure Cloud Storage Solutions" },
  { id: 5, text: "Mobile-First Responsive Design" },
  { id: 6, text: "24/7 Premium Customer Support" },
  { id: 7, text: "Enterprise-Grade Security" },
  { id: 8, text: "Seamless Third-Party Integrations" },
];

export const AnimatedFeatureDisplay = () => {
  const [currentFeatureIndex, setCurrentFeatureIndex] = useState(0);
  const [displayText, setDisplayText] = useState("");
  const [isTyping, setIsTyping] = useState(false);

  useEffect(() => {
    const interval = setInterval(() => {
      // Move to next feature
      setCurrentFeatureIndex((prev) => (prev + 1) % features.length);
    }, 4000); // 4 seconds total cycle

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    const currentFeature = features[currentFeatureIndex];
    setDisplayText(""); // Clear text first
    setIsTyping(true);

    let charIndex = 0;
    const typeInterval = setInterval(() => {
      if (charIndex <= currentFeature.text.length) {
        setDisplayText(currentFeature.text.slice(0, charIndex));
        charIndex++;
      } else {
        clearInterval(typeInterval);
        setIsTyping(false);
      }
    }, 50); // Typing speed

    return () => clearInterval(typeInterval);
  }, [currentFeatureIndex]);

  return (
    <div className="flex items-center justify-center h-full p-6">
      <motion.div
        className="relative bg-white/8 backdrop-blur-lg rounded-2xl p-8 lg:p-10 border border-white/20 shadow-xl max-w-md w-full"
        initial={{ scale: 0.95, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
      >
        {/* Subtle background glow */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent rounded-2xl" />

        <div className="relative z-10 w-full h-24 lg:h-28 flex items-center justify-center">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentFeatureIndex}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{
                duration: 0.4,
                ease: "easeOut"
              }}
              className="text-center"
            >
              <div className="text-base lg:text-lg text-white/95 font-medium min-h-[3rem] flex items-center justify-center px-3 leading-relaxed">
                {displayText}
                {isTyping && (
                  <motion.span
                    className="ml-1 w-0.5 h-5 lg:h-6 bg-white/80 rounded-full"
                    animate={{
                      opacity: [0, 1, 0]
                    }}
                    transition={{
                      duration: 1,
                      repeat: Infinity,
                      ease: "easeInOut"
                    }}
                  />
                )}
              </div>
            </motion.div>
          </AnimatePresence>
        </div>
      </motion.div>
    </div>
  );
};

export default AnimatedFeatureDisplay;
