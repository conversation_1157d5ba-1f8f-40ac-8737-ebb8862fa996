
import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { use3DEffect } from '@/hooks/use3DEffect';
import ThemedHeader from '@/components/layout/ThemedHeader';
import Text3D from '@/components/3D/Text3D';
import { HardDrive, MemoryStick, Cpu } from 'lucide-react';

interface ComponentData {
  name: string;
  description: string;
  image: string;
  function: string;
}

const CircuitComponents = () => {
  const componentRef = use3DEffect({
    intensity: 15,
    perspective: 1000,
    glare: true
  });

  // Component data
  const components: ComponentData[] = [
    {
      name: 'Main Processor',
      description: 'The central processing unit that controls all device operations.',
      image: '/images/processor.png',
      function: 'Executes program instructions and manages device operations.'
    },
    {
      name: 'Flash Memory Chip',
      description: 'Non-volatile storage chip that retains data even when powered off.',
      image: '/images/memory-chip.png',
      function: 'Stores firmware, operating system, and critical device data.'
    },
    {
      name: 'USB Controller',
      description: 'Manages USB communications between the device and connected phones.',
      image: '/images/usb-controller.png',
      function: 'Handles data transfer protocols and connection management.'
    }
  ];

  const [activeComponent, setActiveComponent] = useState(0);

  const currentComponent = components[activeComponent];

  return (
    <section id="circuit-components" className="py-20 bg-pegasus-blue-600/5 dark:from-gray-800/95 dark:via-gray-900/90 dark:to-pegasus-blue-900/25 overflow-hidden relative">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-[0.03]">
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
      </div>

      {/* Animated Gradient Overlay */}
      <div className="absolute inset-0 opacity-100 transition-opacity duration-1000 bg-gradient-to-br from-blue-400/8 via-blue-300/5 to-blue-600/12"></div>

      {/* Subtle Texture Overlay */}
      <div className="absolute inset-0 opacity-[0.02] bg-gradient-to-br from-blue-600/10 via-transparent to-blue-500/10 dark:from-gray-700 dark:via-transparent dark:to-gray-800"></div>

      <div className="container mx-auto px-4 relative z-10">
        <ThemedHeader
          theme="hardware"
          title="Circuit Components"
          subtitle="Explore the key components used in our hardware design and diagnostics"
          highlightWord="Components"
          size="normal"
        />

        <div className="flex flex-col lg:flex-row items-center gap-10 mt-16">
          {/* Left side - Interactive Component Viewer */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: -30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="relative">
              <Card
                ref={componentRef}
                className="p-8 border-pegasus-blue-200 dark:border-pegasus-blue-900 bg-pegasus-blue-600/8 dark:from-gray-900/98 dark:to-gray-800/95 shadow-lg overflow-hidden transform-gpu preserve-3d"
              >
                <div className="flex justify-center mb-6">
                  <div className="relative w-64 h-64 bg-black/5 rounded-lg p-4 flex items-center justify-center">
                    <img
                      src={currentComponent.image}
                      alt={currentComponent.name}
                      className="max-w-full max-h-full object-contain"
                    />
                  </div>
                </div>

                <div className="text-center mb-6">
                  <Text3D
                    as="h3"
                    size="xl"
                    text-pegasus-blue-600
                    className="mb-2"
                  >
                    {currentComponent.name}
                  </Text3D>
                </div>

                <div className="flex justify-center mt-4 gap-2">
                  {components.map((_, index) => (
                    <button
                      key={index}
                      onClick={() => setActiveComponent(index)}
                      className={`w-3 h-3 rounded-full transition-colors ${
                        index === activeComponent ? 'bg-pegasus-blue-500' : 'bg-gray-300 dark:bg-gray-700'
                      }`}
                      aria-label={`View component ${index + 1}`}
                    />
                  ))}
                </div>
              </Card>
            </div>
          </motion.div>

          {/* Right side - Component Description */}
          <motion.div
            className="lg:w-1/2"
            initial={{ opacity: 0, x: 30 }}
            whileInView={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.7 }}
            viewport={{ once: true, margin: "-100px" }}
          >
            <div className="space-y-6">
              <Text3D
                as="h2"
                size="3xl"
                text-pegasus-blue-600
                className="mb-6"
              >
                Key Hardware Components
              </Text3D>

              <p className="text-lg text-gray-700 dark:text-gray-300">
                {currentComponent.description}
              </p>

              <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md border border-gray-100 dark:border-gray-700">
                <h4 className="font-bold text-lg mb-2 text-pegasus-blue-600 dark:text-pegasus-blue-500">
                  Function:
                </h4>
                <p className="text-gray-700 dark:text-gray-300">
                  {currentComponent.function}
                </p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-8">
                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700 text-center">
                  <HardDrive className="h-8 w-8 mx-auto mb-2 text-pegasus-blue-500" />
                  <h5 className="font-medium">Storage</h5>
                </div>

                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700 text-center">
                  <MemoryStick className="h-8 w-8 mx-auto mb-2 text-pegasus-blue-500" />
                  <h5 className="font-medium">Memory</h5>
                </div>

                <div className="p-4 bg-white dark:bg-gray-800 rounded-lg shadow border border-gray-100 dark:border-gray-700 text-center">
                  <Cpu className="h-8 w-8 mx-auto mb-2 text-pegasus-blue-500" />
                  <h5 className="font-medium">Processing</h5>
                </div>
              </div>

              <Button
                className="mt-6 bg-pegasus-blue-500 hover:bg-pegasus-blue-600 text-white"
                onClick={() => document.getElementById('hardware-supported-models')?.scrollIntoView({ behavior: 'smooth' })}
              >
                View Supported Models
              </Button>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Bottom Accent Line */}
      <motion.div
        className="absolute bottom-0 left-0 h-1 w-full bg-gradient-to-r from-transparent via-pegasus-blue-400 to-transparent"
        initial={{ scaleX: 0 }}
        whileInView={{ scaleX: 1 }}
        transition={{ duration: 1.5, delay: 0.5 }}
        viewport={{ once: true }}
      />
    </section>
  );
};

export default CircuitComponents;
