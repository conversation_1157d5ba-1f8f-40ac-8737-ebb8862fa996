import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom";
import { validateSignUpForm, FormErrors } from "@/lib/auth";

export const SimpleSignUpForm = () => {
  const { signUp, signInWithProvider, loading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const formErrors = validateSignUpForm(email, password, confirmPassword);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await signUp(email, password);

      if (!error) {
        // Account created successfully, user should be automatically signed in
        // Navigate to home page
        navigate("/");
      } else {
        setErrors({ general: error.message });
      }
    } catch (err: any) {
      console.error("Sign up error:", err);
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignUp = async () => {
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        toast.error(error.message);
      }
    } catch (err: any) {
      console.error("Google sign up error:", err);
      toast.error("Failed to sign up with Google");
    }
  };



  return (
    <div className="w-full max-w-md mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg space-y-6"
      >
        <div className="text-center">
          <h1 className="text-3xl font-semibold text-white mb-2">Create Account</h1>
          <p className="text-gray-400">Join the Pegasus Tool community</p>
        </div>

        {/* Social Sign Up Button */}
        <div className="space-y-3">
          <Button
            onClick={handleGoogleSignUp}
            variant="outline"
            className="w-full bg-[#1a1a1a] border border-gray-700/50 text-white hover:bg-gray-700/50 hover:border-purple-400/50 transition-all duration-300 backdrop-blur-lg"
            disabled={loading}
          >
            <Chrome className="w-4 h-4 mr-2" />
            Sign up with Google
          </Button>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700/50" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-[#1a1a1a] px-3 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300 font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-[#111111] border border-gray-700/50 text-white placeholder:text-gray-500 focus:border-purple-400/50 focus:ring-purple-400/20 transition-all duration-300 h-12"
              placeholder="Enter your email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300 font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700/50 text-white placeholder:text-gray-500 focus:border-purple-400/50 focus:ring-purple-400/20 transition-all duration-300 h-12 pr-12"
                placeholder="Create a password (min. 6 characters)"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-sm">{errors.password}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="confirmPassword" className="text-sm text-gray-300 font-medium">
              Confirm Password
            </Label>
            <div className="relative">
              <Input
                id="confirmPassword"
                type={showConfirmPassword ? "text" : "password"}
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700/50 text-white placeholder:text-gray-500 focus:border-purple-400/50 focus:ring-purple-400/20 transition-all duration-300 h-12 pr-12"
                placeholder="Confirm your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors"
              >
                {showConfirmPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            {errors.confirmPassword && (
              <p className="text-red-400 text-sm">{errors.confirmPassword}</p>
            )}
          </div>

          {/* Terms and Privacy */}
          <div className="flex items-start space-x-3 p-4 rounded-lg bg-[#111111] border border-gray-700/50">
            <div className="relative">
              <input
                type="checkbox"
                id="terms"
                className="peer h-4 w-4 rounded border-2 border-gray-600 bg-[#111111] text-purple-400 focus:ring-2 focus:ring-purple-400/50 focus:border-purple-400 transition-all duration-200 cursor-pointer"
                required
              />
              <div className="absolute inset-0 rounded border-2 border-transparent bg-gradient-to-r from-purple-400/20 to-purple-500/20 opacity-0 peer-hover:opacity-100 transition-opacity duration-200 pointer-events-none"></div>
            </div>
            <label htmlFor="terms" className="text-sm text-gray-300 leading-relaxed cursor-pointer">
              I agree to the{" "}
              <Link
                to="/terms-of-service"
                className="text-purple-400 hover:text-purple-300 font-medium underline decoration-purple-400/50 hover:decoration-purple-300 transition-all duration-200"
              >
                Terms of Service
              </Link>
              {" "}and{" "}
              <Link
                to="/privacy-policy"
                className="text-purple-400 hover:text-purple-300 font-medium underline decoration-purple-400/50 hover:decoration-purple-300 transition-all duration-200"
              >
                Privacy Policy
              </Link>
            </label>
          </div>

          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">{errors.general}</p>
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email || !password || !confirmPassword}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white font-medium h-12 rounded-lg transition-all duration-300 hover:-translate-y-0.5 shadow-lg hover:shadow-purple-500/25"
          >
            {isSubmitting ? "Creating account..." : "Create Pegasus Account"}
          </Button>
        </form>

        {/* Sign In Link */}
        <div className="text-center pt-2">
          <span className="text-gray-400 text-sm">
            Already have an account?{" "}
            <Link
              to="/sign-in"
              className="text-purple-400 hover:text-purple-300 font-medium hover:underline transition-colors"
            >
              Sign In
            </Link>
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignUpForm;
