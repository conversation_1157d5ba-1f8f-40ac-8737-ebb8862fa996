import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getTheme, motionVariants, spacing, ThemeType } from '@/styles/design-system';

interface StandardSectionProps {
  theme?: ThemeType;
  children: React.ReactNode;
  className?: string;
  variant?: 'primary' | 'secondary' | 'hero' | 'neutral';
  size?: 'normal' | 'large' | 'compact';
  withPattern?: boolean;
  withOverlay?: boolean;
  withAccentLine?: boolean;
  id?: string;
  animate?: boolean;
  animationVariant?: 'fadeInUp' | 'fadeInLeft' | 'fadeInRight' | 'scaleIn';
  customBackground?: string;
}

const StandardSection: React.FC<StandardSectionProps> = ({
  theme = 'neutral',
  children,
  className,
  variant = 'primary',
  size = 'normal',
  withPattern = true,
  withOverlay = true,
  withAccentLine = true,
  id,
  animate = true,
  animationVariant = 'fadeInUp',
  customBackground
}) => {
  const themeConfig = getTheme(theme);

  const getBackgroundClass = () => {
    if (customBackground) return customBackground;

    switch (variant) {
      case 'hero':
        return `pt-28 pb-20 ${themeConfig.primaryGradient}`;
      case 'secondary':
        return themeConfig.secondaryGradient;
      case 'neutral':
        return 'bg-gray-600/3 dark:from-gray-900/98 dark:via-gray-800/95 dark:to-gray-700/30';
      default:
        return themeConfig.primaryGradient;
    }
  };

  const getSizeClass = () => {
    switch (size) {
      case 'large':
        return spacing.sectionLarge;
      case 'compact':
        return 'py-16';
      default:
        return spacing.section;
    }
  };

  const getOverlayGradient = () => {
    if (theme === 'hardware') {
      return 'bg-gradient-to-br from-pegasus-blue-400/8 via-pegasus-blue-300/5 to-pegasus-blue-600/12';
    } else if (theme === 'software') {
      return 'bg-gradient-to-br from-orange-400/8 via-orange-300/5 to-orange-600/12';
    } else if (theme === 'purple') {
      return 'bg-gradient-to-br from-purple-400/8 via-purple-300/5 to-purple-600/12';
    }
    return 'bg-gradient-to-br from-gray-400/8 via-gray-300/5 to-gray-600/12';
  };

  const getAccentLineColor = () => {
    if (theme === 'hardware') {
      return 'bg-gradient-to-r from-transparent via-pegasus-blue-400 to-transparent';
    } else if (theme === 'software') {
      return 'bg-gradient-to-r from-transparent via-orange-400 to-transparent';
    } else if (theme === 'purple') {
      return 'bg-gradient-to-r from-transparent via-[#C084FC] to-transparent';
    }
    return 'bg-gradient-to-r from-transparent via-gray-400 to-transparent';
  };

  const sectionContent = (
    <section
      id={id}
      className={cn(
        'relative overflow-hidden',
        getSizeClass(),
        getBackgroundClass(),
        className
      )}
    >
      {/* Background Pattern */}
      {withPattern && (
        <div className="absolute inset-0 opacity-[0.03]">
          <div className="absolute inset-0 bg-[url('/patterns/dots.svg')]"></div>
        </div>
      )}

      {/* Animated Gradient Overlay */}
      {withOverlay && (
        <div className={cn(
          'absolute inset-0 opacity-100 transition-opacity duration-1000',
          getOverlayGradient()
        )}></div>
      )}

      {/* Subtle Texture Overlay */}
      <div className={cn(
        'absolute inset-0 opacity-[0.02]',
        theme === 'hardware' ? 'bg-gradient-to-br from-pegasus-blue-600/10 via-transparent to-pegasus-blue-500/10 dark:from-gray-700 dark:via-transparent dark:to-gray-800' :
        theme === 'software' ? 'bg-gradient-to-br from-orange-100 via-transparent to-orange-200 dark:from-gray-700 dark:via-transparent dark:to-gray-800' :
        theme === 'purple' ? 'bg-gradient-to-br from-purple-600/10 via-transparent to-purple-500/10 dark:from-purple-700 dark:via-transparent dark:to-purple-800' :
        'bg-gradient-to-br from-gray-600/10 via-transparent to-gray-500/10 dark:from-gray-700 dark:via-transparent dark:to-gray-800'
      )}></div>

      {/* Content Container */}
      <div className={cn('relative z-10', spacing.container)}>
        {children}
      </div>

      {/* Bottom Accent Line */}
      {withAccentLine && (
        <motion.div
          className={cn(
            'absolute bottom-0 left-0 h-1 w-full',
            getAccentLineColor()
          )}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 1.5, delay: 0.5 }}
          viewport={{ once: true }}
        />
      )}
    </section>
  );

  if (!animate) {
    return sectionContent;
  }

  const animationConfig = motionVariants[animationVariant];

  return (
    <motion.div
      initial={animationConfig.initial}
      whileInView={animationConfig.animate}
      transition={animationConfig.transition}
      viewport={{ once: true, margin: "-100px" }}
    >
      {sectionContent}
    </motion.div>
  );
};

export default StandardSection;
