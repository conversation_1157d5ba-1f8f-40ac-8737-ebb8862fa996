
import { useEffect, useRef, useState } from 'react';

interface InViewOptions {
  threshold?: number;
  triggerOnce?: boolean;
  rootMargin?: string;
  delay?: number;
}

/**
 * Enhanced hook to detect when an element is in the viewport
 * with additional options for animation control
 */
export const useInView = ({
  threshold = 0.1,
  triggerOnce = false,
  rootMargin = '0px',
  delay = 0
}: InViewOptions = {}) => {
  const ref = useRef<HTMLDivElement>(null);
  const [inView, setInView] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  useEffect(() => {
    const currentRef = ref.current;
    if (!currentRef) return;

    const options = {
      threshold,
      rootMargin,
      root: null
    };

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (hasTriggered && triggerOnce) return;

        if (entry.isIntersecting) {
          if (delay > 0) {
            requestAnimationFrame(() => {
              setTimeout(() => {
                setInView(true);
                setHasTriggered(true);
              }, delay);
            });
          } else {
            setInView(true);
            setHasTriggered(true);
          }
        } else {
          if (!triggerOnce) setInView(false);
        }
      },
      options
    );

    observer.observe(currentRef);
    return () => observer.disconnect();
  }, [delay, hasTriggered, rootMargin, threshold, triggerOnce]);

  return { ref, inView };
};
