
import { useCallback } from 'react';

export const useAnimationUtils = () => {
  // Defers animations for better performance
  // تحسين دالة deferAnimation باستخدام requestAnimationFrame
  const deferAnimation = useCallback((callback, delay = 100) => {
    requestAnimationFrame(() => {
      if ('requestIdleCallback' in window) {
        window.requestIdleCallback(() => {
          setTimeout(callback, delay);
        });
      } else {
        setTimeout(callback, delay);
      }
    });
  }, []);

  // Handle staggered animations with enhanced options
  const staggeredReveal = useCallback(
    (elements,
    baseDelay = 100,
    baseClass = 'opacity-0',
    activeClass = 'opacity-100 translate-y-0',
    staggerAmount = 50,
    easingFunction = 'cubic-bezier(0.25, 0.1, 0.25, 1.0)') => {
      elements.forEach((element, index) => {
        // Set initial state
        element.classList.add(baseClass, 'transition-all', 'duration-500');
        element.style.transitionDelay = `${baseDelay + (index * staggerAmount)}ms`;
        element.style.transitionTimingFunction = easingFunction;
        
        // Use RAF to batch changes
        requestAnimationFrame(() => {
          deferAnimation(() => {
            element.classList.remove(baseClass);
            element.classList.add(activeClass);
          }, 10);
        });
      });
    }, 
  [deferAnimation]);

  // Create glowing effect on elements
  const createGlowEffect = useCallback((element, color = 'rgba(249, 115, 22, 0.6)', intensity = 20) => {
    if (!element) return () => {};
    
    const originalBoxShadow = element.style.boxShadow;
    const originalTransition = element.style.transition;
    
    element.style.transition = 'box-shadow 0.3s ease-in-out, transform 0.2s ease-in-out';
    
    const handleMouseEnter = () => {
      element.style.boxShadow = `0 0 ${intensity}px ${color}`;
      element.style.transform = 'scale(1.03)';
    };
    
    const handleMouseLeave = () => {
      element.style.boxShadow = originalBoxShadow;
      element.style.transform = 'scale(1)';
    };
    
    element.addEventListener('mouseenter', handleMouseEnter);
    element.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      element.removeEventListener('mouseenter', handleMouseEnter);
      element.removeEventListener('mouseleave', handleMouseLeave);
      element.style.boxShadow = originalBoxShadow;
      element.style.transition = originalTransition;
      element.style.transform = '';
    };
  }, []);

  // Create interactive floating effect
  // تحسين createFloatingEffect باستخدام transform3d للتسريع بواسطة GPU
  const createFloatingEffect = useCallback((element, amount = 10) => {
    if (!element) return () => {};
    
    const originalTransform = element.style.transform;
    const originalTransition = element.style.transition;
    
    element.style.transition = 'transform 2s ease-in-out';
    element.style.willChange = 'transform'; // إضافة تلميح للمتصفح
    
    let animationFrame;
    let startTime;
    
    const animate = (timestamp) => {
      if (!startTime) startTime = timestamp;
      const elapsed = timestamp - startTime;
      
      const floatY = Math.sin(elapsed / 1000) * amount;
      element.style.transform = `translate3d(0, ${floatY}px, 0)`;
      
      animationFrame = requestAnimationFrame(animate);
    };
    
    animationFrame = requestAnimationFrame(animate);
    
    return () => {
      cancelAnimationFrame(animationFrame);
      element.style.transform = originalTransform;
      element.style.transition = originalTransition;
      element.style.willChange = 'auto';
    };
  }, []);

  return {
    deferAnimation,
    staggeredReveal,
    createGlowEffect,
    createFloatingEffect
  };
};
