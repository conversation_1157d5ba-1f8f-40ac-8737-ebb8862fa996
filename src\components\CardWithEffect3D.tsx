
import React, { useState } from "react";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { motion } from "framer-motion";
import { use3DEffect } from "@/hooks/use3DEffect";

interface CardWithEffect3DProps {
  children: React.ReactNode;
  className?: string;
  delay?: number;
  variant?: "default" | "elegant" | "gradient" | "glow" | "glass" | "neo" | "3d";
  depth?: "none" | "low" | "medium" | "high";
  interactive?: boolean;
  onClick?: () => void;
}

const CardWithEffect3D: React.FC<CardWithEffect3DProps> = ({
  children,
  className,
  delay = 0,
  variant = "default",
  depth = "medium",
  interactive = true,
  onClick,
}) => {
  const ref = use3DEffect({
    intensity: 15,
    perspective: 1000,
    glare: variant === "glass" || variant === "glow",
    scale: true
  });

  const [isHovered, setIsHovered] = useState(false);

  const getVariantClasses = () => {
    switch (variant) {
      case "elegant":
        return "bg-orange-600/8 dark:bg-gray-800 border border-pegasus-orange/20 dark:border-pegasus-orange/10";
      case "gradient":
        return "bg-orange-600/8 dark:from-gray-800 dark:to-gray-900";
      case "glow":
        return "bg-orange-600/8 dark:bg-gray-800 border border-pegasus-orange/20 dark:border-pegasus-orange/10 shadow-glow";
      case "glass":
        return "bg-gray-600/5 dark:bg-gray-800/80 backdrop-blur-md border border-gray-200/20 dark:border-gray-700/30";
      case "neo":
        return "bg-gray-600/5 dark:bg-gray-800 border-none shadow-[5px_5px_15px_#d9d9d9,-5px_-5px_15px_#f5f5f5] dark:shadow-[5px_5px_15px_#1a1a1a,-5px_-5px_15px_#2c2c2c]";
      case "3d":
        return "bg-gray-600/5 dark:bg-gray-800 before:absolute before:inset-0 before:-z-10 before:translate-y-2 before:translate-x-2 before:bg-pegasus-orange/20 before:blur-sm before:rounded-lg";
      default:
        return "bg-gray-600/5 dark:bg-gray-800";
    }
  };

  const getDepthClasses = () => {
    switch (depth) {
      case "none":
        return "";
      case "low":
        return "shadow-md";
      case "medium":
        return "shadow-lg";
      case "high":
        return "shadow-xl";
      default:
        return "shadow-md";
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      viewport={{ once: true, margin: "-50px" }}
      transition={{ duration: 0.5, delay: delay * 0.2 }}
      className={cn("perspective-1000", onClick && "cursor-pointer")}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={onClick}
    >
      <Card
        ref={interactive ? ref : undefined}
        className={cn(
          "rounded-lg overflow-hidden transform-gpu transition-all duration-300",
          getVariantClasses(),
          getDepthClasses(),
          className
        )}
      >
        {interactive && variant === "glow" && isHovered && (
          <div className="absolute inset-0 bg-pegasus-orange/10 animate-pulse pointer-events-none" />
        )}

        {children}
      </Card>
    </motion.div>
  );
};

export default CardWithEffect3D;
