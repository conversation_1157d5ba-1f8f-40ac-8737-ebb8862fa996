import { AuthError } from '@supabase/supabase-js';

// Auth error handling utilities
export const getAuthErrorMessage = (error: AuthError): string => {
  switch (error.message) {
    case 'Invalid login credentials':
      return 'Invalid email or password';
    case 'Email not confirmed':
      return 'Please check your email and click the verification link';
    case 'User already registered':
      return 'An account with this email already exists';
    case 'Password should be at least 6 characters':
      return 'Password must be at least 6 characters long';
    case 'Unable to validate email address: invalid format':
      return 'Please enter a valid email address';
    case 'Email rate limit exceeded':
      return 'Too many emails sent. Please wait before trying again';
    case 'For security purposes, you can only request this once every 60 seconds':
      return 'Please wait 60 seconds before requesting another password reset';
    default:
      return error.message || 'An unexpected error occurred';
  }
};

// Validation utilities
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): { isValid: boolean; message?: string } => {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long' };
  }
  if (password.length > 72) {
    return { isValid: false, message: 'Password must be less than 72 characters' };
  }
  return { isValid: true };
};

// User profile utilities
export const getUserDisplayName = (user: any): string => {
  if (user?.user_metadata?.full_name) {
    return user.user_metadata.full_name;
  }
  if (user?.user_metadata?.name) {
    return user.user_metadata.name;
  }
  if (user?.email) {
    return user.email.split('@')[0];
  }
  return 'User';
};

export const getUserAvatar = (user: any): string | null => {
  if (user?.user_metadata?.avatar_url) {
    return user.user_metadata.avatar_url;
  }
  if (user?.user_metadata?.picture) {
    return user.user_metadata.picture;
  }
  return null;
};

// Session utilities
export const isSessionValid = (session: any): boolean => {
  if (!session) return false;
  
  const now = Math.floor(Date.now() / 1000);
  return session.expires_at > now;
};

// Auth state utilities
export const getAuthState = (user: any, loading: boolean) => {
  return {
    isAuthenticated: !!user,
    isLoading: loading,
    user: user,
  };
};

// Form validation
export interface FormErrors {
  [key: string]: string;
}

export const validateSignUpForm = (email: string, password: string, confirmPassword?: string): FormErrors => {
  const errors: FormErrors = {};

  if (!email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(email)) {
    errors.email = 'Please enter a valid email address';
  }

  if (!password) {
    errors.password = 'Password is required';
  } else {
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.message!;
    }
  }

  if (confirmPassword !== undefined && password !== confirmPassword) {
    errors.confirmPassword = 'Passwords do not match';
  }

  return errors;
};

export const validateSignInForm = (email: string, password: string): FormErrors => {
  const errors: FormErrors = {};

  if (!email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(email)) {
    errors.email = 'Please enter a valid email address';
  }

  if (!password) {
    errors.password = 'Password is required';
  }

  return errors;
};

export const validateResetPasswordForm = (email: string): FormErrors => {
  const errors: FormErrors = {};

  if (!email) {
    errors.email = 'Email is required';
  } else if (!validateEmail(email)) {
    errors.email = 'Please enter a valid email address';
  }

  return errors;
};
