
import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { motion } from "framer-motion";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/components/ui/use-toast";
import { toast } from "sonner";
import { ArrowRight, Download, CheckCircle2, Smartphone, Zap, RefreshCw } from "lucide-react";
import SectionHeader from '@/components/SectionHeader';
import AnimatedCard from '@/components/AnimatedCard';
import AnimatedCounter from '@/components/AnimatedCounter';
import SupportedModels from '@/sections/SupportedModels';
import Pricing from '@/sections/Pricing';
import ThemedSection from '@/components/layout/ThemedSection';
import ThemedHeader from '@/components/layout/ThemedHeader';
import StandardSection from '@/components/layout/StandardSection';
import { use3DEffect } from '@/hooks/use3DEffect';
import { CardContent } from '@/components/ui/card';

const container = {
  hidden: { opacity: 0 },
  show: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1,
      delayChildren: 0.3
    }
  }
};

const item = {
  hidden: { opacity: 0, y: 20 },
  show: { opacity: 1, y: 0, transition: { duration: 0.5 } }
};

const Software = () => {
  const { toast: toastNotify } = useToast();
  const [isVisible, setIsVisible] = useState(false);
  const [homeImageUrl, setHomeImageUrl] = useState("/lovable-uploads/46319556-27d1-46f3-b365-81927d12674f.png");
  const [latestUpdate, setLatestUpdate] = useState<{
    varizon: string;
    name: string | null;
    link: string | null;
  } | null>(null);

  // Add 3D effect for the software image card
  const softwareImageRef = use3DEffect({
    intensity: 20,
    perspective: 1000,
    glare: true,
    scale: true
  });
  const [stats, setStats] = useState({
    totalModels: 0,
    downloadCount: 0,
    distributorsCount: 0
  });

  useEffect(() => {
    setIsVisible(true);

    // Fetch home image from Supabase storage
    const fetchHomeImage = async () => {
      try {
        // Get home image from Supabase storage
        const {
          data: imageData
        } = await supabase.storage.from('website').getPublicUrl('Images/Home/home.png');

        if (imageData) {
          setHomeImageUrl(imageData.publicUrl);
        }
      } catch (error) {
        console.error('Error fetching home image:', error);
        toastNotify({
          title: "Image Loading Error",
          description: "Could not load the home image. Using fallback image instead.",
          variant: "destructive",
        });
      }
    };

    // Fetch latest software update
    const fetchLatestUpdate = async () => {
      try {
        const { data, error } = await supabase
          .from('update')
          .select('varizon, name, link')
          .order('release_at', { ascending: false })
          .limit(1);

        if (error) throw error;
        if (data && data.length > 0) {
          setLatestUpdate(data[0]);
        }
      } catch (error) {
        console.error('Error fetching latest update:', error);
      }
    };

    // Fetch statistics
    const fetchStats = async () => {
      try {
        // Get total models count
        const { data: modelSettings, error: modelError } = await supabase
          .from('settings')
          .select('numeric_value')
          .eq('key', 'total_models')
          .single();

        if (modelError) console.error('Error fetching total models:', modelError);

        // Get download count
        const { data: updateData, error: updateError } = await supabase
          .from('update')
          .select('download_count')
          .order('release_at', { ascending: false })
          .limit(1);

        if (updateError) console.error('Error fetching download count:', updateError);

        setStats({
          totalModels: modelSettings?.numeric_value || 0,
          downloadCount: updateData?.[0]?.download_count || 0,
          distributorsCount: stats.distributorsCount
        });
      } catch (error) {
        console.error('Error fetching stats:', error);
      }
    };

    fetchHomeImage();
    fetchLatestUpdate();
    fetchStats();
  }, []);

  const handleDownload = async () => {
    try {
      if (latestUpdate?.link) {
        // Call the increment_counter function
        const { data, error: counterError } = await supabase.rpc('increment_counter');

        if (counterError) {
          console.error('Error incrementing download counter:', counterError);
          toast.error('Failed to process download request');
        } else {
          console.log('Download count increased to:', data);

          // Open the download link
          window.location.href = latestUpdate.link;
          toast.success('Download started!');
        }
      } else {
        toast.info("Download link is not available at the moment. Please try again later.");
      }
    } catch (error) {
      console.error('Error during download:', error);
      // Still provide download link even if counting fails
      if (latestUpdate?.link) {
        window.location.href = latestUpdate.link;
      }
    }
  };

  return (
    <div>
      {/* Hero Section */}
      <section className="pt-28 pb-20 bg-gradient-to-br from-slate-900 via-orange-900/90 to-orange-700/80 dark:from-gray-900 dark:via-slate-800/95 dark:to-orange-900/70 overflow-hidden relative">
        {/* Enhanced background pattern */}
        <div className="absolute inset-0 bg-[url('/patterns/dots.svg')] opacity-10"></div>
        <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent"></div>

        {/* Enhanced animated background elements */}
        <motion.div
          className="absolute top-20 right-20 w-80 h-80 bg-gradient-to-r from-orange-500/15 to-orange-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.4, 0.6, 0.4],
            rotate: [0, 180, 360]
          }}
          transition={{
            duration: 12,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>
        <motion.div
          className="absolute bottom-10 left-10 w-60 h-60 bg-gradient-to-r from-orange-500/15 to-orange-400/10 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.4, 1],
            opacity: [0.3, 0.5, 0.3],
            rotate: [360, 180, 0]
          }}
          transition={{
            duration: 15,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-orange-400/5 to-orange-300/5 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{
            duration: 20,
            repeat: Infinity,
            repeatType: "reverse"
          }}
        ></motion.div>

        <div className="container mx-auto px-4 relative">

          <div className="flex flex-col md:flex-row items-center">
            <motion.div
              className="md:w-1/2 mb-10 md:mb-0"
              initial={{ opacity: 0, x: -50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 text-slate-200 font-montserrat tracking-tight leading-tight">
                Pegasus <span className="bg-gradient-to-r from-orange-400 to-orange-300 bg-clip-text text-transparent">Software</span>
              </h1>

              <motion.p
                className="text-lg md:text-xl text-slate-200 mb-8 max-w-lg leading-relaxed"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.3, duration: 0.7 }}
                style={{
                  textShadow: '0 2px 4px rgba(0, 0, 0, 0.3)'
                }}
              >
                Comprehensive <span className="text-orange-300 font-semibold">software solution</span> for device unlocking, firmware flashing, and repair operations. Supporting <span className="text-orange-300 font-semibold">200+ smartphone models</span> across all major brands with enterprise-grade reliability.
              </motion.p>

              <motion.div
                className="space-y-4 md:space-y-0 md:space-x-4 flex flex-col md:flex-row"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.5, duration: 0.7 }}
              >
                <Button
                  className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white px-8 py-6 rounded-2xl text-lg w-full md:w-auto transition-all duration-500 hover:-translate-y-2 shadow-xl hover:shadow-2xl flex items-center justify-center group border border-orange-400/20 backdrop-blur-sm"
                  onClick={handleDownload}
                  style={{
                    boxShadow: '0 10px 25px rgba(251, 146, 60, 0.3), 0 0 0 1px rgba(251, 146, 60, 0.1)'
                  }}
                >
                  <Download className="mr-3 h-6 w-6 group-hover:scale-125 group-hover:rotate-12 transition-all duration-300" />
                  <span className="relative font-semibold">
                    Get Pegasus Tool {latestUpdate && `v${latestUpdate.varizon}`}
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-white/40 scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                  </span>
                </Button>

                <Button
                  className="bg-transparent border-2 border-orange-400/60 hover:border-orange-300 text-white px-8 py-6 rounded-2xl text-lg w-full md:w-auto transition-all duration-500 hover:-translate-y-2 shadow-xl hover:shadow-2xl hover:bg-gradient-to-r hover:from-orange-500/20 hover:to-orange-400/20 flex items-center justify-center group backdrop-blur-sm"
                  onClick={() => document.getElementById('software-supported-models')?.scrollIntoView({ behavior: 'smooth' })}
                  style={{
                    boxShadow: '0 10px 25px rgba(251, 146, 60, 0.2), 0 0 0 1px rgba(251, 146, 60, 0.1)'
                  }}
                >
                  <ArrowRight className="mr-3 h-6 w-6 group-hover:scale-125 group-hover:translate-x-1 transition-all duration-300" />
                  <span className="relative font-semibold">
                    Explore Features
                    <span className="absolute bottom-0 left-0 w-full h-0.5 bg-orange-300/40 scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
                  </span>
                </Button>
              </motion.div>
            </motion.div>

            <motion.div
              className="md:w-1/2 relative z-10"
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.7, ease: "easeOut" }}
            >
              <div className="relative flex items-center justify-center">
                {/* Enhanced glowing background circle */}
                <motion.div
                  className="bg-gradient-to-br from-orange-500/25 via-orange-400/20 to-orange-700/15 rounded-full h-72 w-72 md:h-[420px] md:w-[420px] mx-auto absolute"
                  animate={{
                    scale: [1, 1.08, 1],
                    opacity: [0.6, 0.9, 0.6],
                    rotate: [0, 360]
                  }}
                  transition={{
                    duration: 8,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>

                {/* Secondary glow effect */}
                <motion.div
                  className="bg-gradient-to-br from-orange-400/15 to-orange-300/10 rounded-full h-80 w-80 md:h-[480px] md:w-[480px] mx-auto absolute blur-xl"
                  animate={{
                    scale: [1, 1.15, 1],
                    opacity: [0.3, 0.6, 0.3]
                  }}
                  transition={{
                    duration: 12,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                ></motion.div>

                <motion.div
                  ref={softwareImageRef}
                  className="relative z-10 max-w-full md:max-w-md mx-auto bg-gradient-to-br from-slate-800/40 via-slate-700/30 to-slate-900/40 backdrop-blur-sm rounded-2xl p-6 preserve-3d border border-orange-500/20 shadow-2xl"
                  animate={{
                    y: [0, -12, 0],
                  }}
                  transition={{
                    duration: 7,
                    repeat: Infinity,
                    repeatType: "reverse"
                  }}
                  style={{
                    maxHeight: '500px',
                    boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(251, 146, 60, 0.1)'
                  }}
                >
                  <img
                    src={homeImageUrl}
                    alt="Pegasus Tool - Advanced Mobile Device Management Interface"
                    className="w-full h-full object-contain rounded-lg"
                    style={{
                      filter: 'drop-shadow(0 15px 25px rgba(0, 0, 0, 0.6)) brightness(1.1) contrast(1.05)'
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.src = "/lovable-uploads/46319556-27d1-46f3-b365-81927d12674f.png";
                    }}
                  />

                  {/* Enhanced glowing circuit points */}
                  <motion.div
                    className="absolute top-1/4 left-1/4 w-4 h-4 bg-orange-400 rounded-full shadow-lg"
                    animate={{
                      scale: [1, 1.3, 1],
                      opacity: [0.7, 1, 0.7],
                      boxShadow: ['0 0 10px rgba(251, 146, 60, 0.5)', '0 0 20px rgba(251, 146, 60, 0.8)', '0 0 10px rgba(251, 146, 60, 0.5)']
                    }}
                    transition={{
                      duration: 2,
                      repeat: Infinity,
                      repeatType: "reverse"
                    }}
                  ></motion.div>
                  <motion.div
                    className="absolute top-1/2 left-1/3 w-3 h-3 bg-orange-300 rounded-full shadow-lg"
                    animate={{
                      scale: [1, 1.4, 1],
                      opacity: [0.6, 1, 0.6],
                      boxShadow: ['0 0 8px rgba(253, 186, 116, 0.5)', '0 0 16px rgba(253, 186, 116, 0.8)', '0 0 8px rgba(253, 186, 116, 0.5)']
                    }}
                    transition={{
                      duration: 2.5,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 0.5
                    }}
                  ></motion.div>
                  <motion.div
                    className="absolute bottom-1/3 right-1/4 w-4 h-4 bg-orange-200 rounded-full shadow-lg"
                    animate={{
                      scale: [1, 1.2, 1],
                      opacity: [0.8, 1, 0.8],
                      boxShadow: ['0 0 12px rgba(254, 215, 170, 0.5)', '0 0 24px rgba(254, 215, 170, 0.8)', '0 0 12px rgba(254, 215, 170, 0.5)']
                    }}
                    transition={{
                      duration: 3,
                      repeat: Infinity,
                      repeatType: "reverse",
                      delay: 1
                    }}
                  ></motion.div>
                </motion.div>
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <ThemedSection
        theme="software"
        id="software-features"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="software"
          title="Software Features"
          subtitle="Discover the powerful features of Pegasus Tool software"
          highlightWord="Features"
          size="normal"
        />

        <motion.div
          className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-7xl mx-auto mt-16"
          variants={container}
          initial="hidden"
          whileInView="show"
          viewport={{ once: true, margin: "-100px" }}
        >
          {[
            {
              title: 'Professional Interface',
              icon: CheckCircle2,
              color: 'from-pegasus-orange to-red-500',
              description: 'Enterprise-grade interface designed for technicians and repair professionals with streamlined workflows and advanced diagnostics.'
            },
            {
              title: 'Universal Compatibility',
              icon: Smartphone,
              color: 'from-blue-500 to-purple-600',
              description: 'Comprehensive support for 200+ device models including Xiaomi, Samsung, Oppo, Vivo, Realme, and emerging brands.'
            },
            {
              title: 'Continuous Innovation',
              icon: RefreshCw,
              color: 'from-green-500 to-teal-500',
              description: 'Regular updates with new device support, security patches, and cutting-edge features to stay ahead of industry demands.'
            }
          ].map((feature, index) => (
            <motion.div key={index} variants={item}>
              <AnimatedCard
                variant="elegant"
                hoverEffect="lift"
                delay={index * 0.1}
                className="h-full cursor-pointer"
              >
                <CardContent className="flex flex-col items-center text-center p-8">
                  <div className={`w-16 h-16 rounded-full bg-gradient-to-br ${feature.color} flex items-center justify-center mb-4`}>
                    <feature.icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold mb-2 text-gray-900 dark:text-white">{feature.title}</h3>
                  <p className="text-gray-600 dark:text-gray-400">
                    {feature.description}
                  </p>
                </CardContent>
              </AnimatedCard>
            </motion.div>
          ))}
        </motion.div>
      </ThemedSection>

      {/* Supported Models Section */}
      <ThemedSection
        theme="software"
        id="software-supported-models"
        variant="primary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="software"
          title="Supported Device Models"
          subtitle="Complete software compatibility for unlocking and repair solutions"
          highlightWord="Device"
          size="normal"
        />
        <SupportedModels theme="software" />
      </ThemedSection>

      {/* Pricing Section */}
      <ThemedSection
        theme="software"
        id="software-pricing"
        variant="secondary"
        withParallax={true}
        animationVariant="fadeInUp"
      >
        <ThemedHeader
          theme="software"
          title="Software Solutions Pricing"
          subtitle="Complete software tools for device unlocking and repair with flexible pricing options"
          highlightWord="Pricing"
          size="normal"
        />
        <Pricing theme="software" />
      </ThemedSection>
    </div>
  );
};

export default Software;



