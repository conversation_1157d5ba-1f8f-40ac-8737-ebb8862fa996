
import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search, MessageCircle, PhoneCall, Mail, ArrowRight, ChevronDown, ChevronUp,
  Headphones, Clock, Globe, Shield, Zap, Users, Star, TrendingUp,
  CheckCircle, AlertCircle, HelpCircle, BookOpen, Video, FileText
} from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { toast } from 'sonner';

const HelpCenter = () => {
  const [expanded, setExpanded] = useState<number | null>(null);
  const [contactForm, setContactForm] = useState({
    name: '',
    email: '',
    message: ''
  });

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };

  // Handle expanding/collapsing FAQ items
  const toggleExpand = (index: number) => {
    if (expanded === index) {
      setExpanded(null);
    } else {
      setExpanded(index);
    }
  };

  // Handle search
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    const formData = new FormData(e.target as HTMLFormElement);
    const searchQuery = formData.get('search') as string;
    
    if (searchQuery.trim()) {
      toast.info(`Searching for help with: ${searchQuery}`);
    } else {
      toast.warning('Please enter a search query');
    }
  };

  // Handle contact form
  const handleContactSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!contactForm.name || !contactForm.email || !contactForm.message) {
      toast.error('Please fill out all fields');
      return;
    }
    
    // Form validation passed
    toast.success('Your message has been sent! We will get back to you soon.');
    setContactForm({
      name: '',
      email: '',
      message: ''
    });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setContactForm(prev => ({
      ...prev,
      [name]: value
    }));
  };

  // FAQ data
  const faqs = [
    {
      question: "How do I install Pegasus Tool?",
      answer: "Download the latest version from our website, run the installer, and follow the on-screen instructions. Make sure to run the installer as administrator."
    },
    {
      question: "Why is my device not being detected?",
      answer: "First, ensure your device has USB debugging enabled. Second, install the appropriate drivers for your device. Finally, try using a different USB cable or port."
    },
    {
      question: "How do I update to the latest version?",
      answer: "You can update directly from within the application by going to Help > Check for Updates, or download the latest version from our website and install it over your current installation."
    },
    {
      question: "Is my device supported?",
      answer: "Check our Supported Models section to see if your specific device model is listed. We regularly add support for new devices with each update."
    },
    {
      question: "How do I activate my license?",
      answer: "After purchase, you'll receive an activation key by email. Open Pegasus Tool, go to Settings > License, and enter your activation key."
    }
  ];

  // Support stats
  const supportStats = [
    { icon: Clock, value: "< 2 hrs", label: "Average Response Time", color: "text-blue-400" },
    { icon: Users, value: "24/7", label: "Support Availability", color: "text-green-400" },
    { icon: Star, value: "4.9/5", label: "Customer Satisfaction", color: "text-yellow-400" },
    { icon: Globe, value: "15+", label: "Languages Supported", color: "text-purple-400" },
  ];

  // Contact methods
  const contactMethods = [
    {
      method: "Live Chat",
      description: "Get instant help from our support experts",
      icon: MessageCircle,
      color: "from-blue-500 to-blue-600",
      availability: "Available 24/7",
      action: () => toast.info("Initiating live chat with support...")
    },
    {
      method: "Phone Support",
      description: "Speak directly with our technical team",
      icon: PhoneCall,
      color: "from-green-500 to-green-600",
      availability: "Mon-Fri 9AM-6PM",
      action: () => toast.info("Opening phone support details...")
    },
    {
      method: "Email Support",
      description: "Send detailed questions and get comprehensive answers",
      icon: Mail,
      color: "from-purple-500 to-purple-600",
      availability: "Response within 2 hours",
      action: () => toast.info("Preparing email support form...")
    },
    {
      method: "Video Call",
      description: "Schedule a screen-sharing session for complex issues",
      icon: Video,
      color: "from-orange-500 to-red-500",
      availability: "By appointment",
      action: () => toast.info("Scheduling video call support...")
    }
  ];

  // Help resources
  const helpResources = [
    {
      title: "Video Tutorials",
      description: "Step-by-step video guides for common tasks",
      icon: Video,
      color: "text-red-400",
      count: "50+ videos",
      action: () => toast.info("Opening video tutorials...")
    },
    {
      title: "Documentation",
      description: "Comprehensive guides and technical documentation",
      icon: BookOpen,
      color: "text-blue-400",
      count: "200+ articles",
      action: () => toast.info("Opening documentation...")
    },
    {
      title: "Community Forum",
      description: "Connect with other users and share solutions",
      icon: Users,
      color: "text-green-400",
      count: "5K+ members",
      action: () => toast.info("Opening community forum...")
    },
    {
      title: "Download Center",
      description: "Get the latest software versions and drivers",
      icon: FileText,
      color: "text-purple-400",
      count: "Latest updates",
      action: () => toast.info("Opening download center...")
    }
  ];

  return (
    <div className="min-h-screen bg-[#111111] text-gray-300 relative overflow-hidden">
      {/* Background Effects */}
      <div className="absolute inset-0 z-0 pointer-events-none" style={{
        background: 'linear-gradient(to bottom, transparent 0%, #111111 90%), radial-gradient(ellipse at center, transparent 40%, #111111 95%)'
      }}></div>

      {/* Animated particles */}
      {[...Array(10)].map((_, i) => (
        <motion.div
          key={i}
          className="absolute rounded-full bg-purple-500/5"
          style={{
            width: Math.random() * 100 + 50,
            height: Math.random() * 100 + 50,
            top: `${Math.random() * 100}%`,
            left: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -200, 0],
            x: [0, Math.random() * 100 - 50, 0],
            opacity: [0, 0.3, 0],
          }}
          transition={{
            duration: Math.random() * 20 + 15,
            repeat: Infinity,
            delay: Math.random() * 10,
          }}
        />
      ))}

      <div className="relative z-10 pt-20 pb-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Hero Section */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            className="text-center mb-16"
          >
            <motion.div variants={itemVariants} className="mb-6">
              <span className="bg-[#1a1a1a] border border-gray-700 text-purple-400 px-4 py-1 rounded-full text-sm font-medium">
                Support Center
              </span>
            </motion.div>

            <motion.h1 variants={itemVariants} className="text-4xl md:text-5xl font-bold text-white mb-4">
              How can we <span className="text-purple-400">help you</span> today?
            </motion.h1>

            <motion.p variants={itemVariants} className="text-lg text-gray-400 max-w-3xl mx-auto mb-10">
              Get instant support, find answers to your questions, and connect with our expert team
            </motion.p>

            {/* Search section */}
            <motion.form variants={itemVariants} onSubmit={handleSearch} className="max-w-2xl mx-auto">
              <div className="relative flex items-center">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  name="search"
                  type="text"
                  placeholder="Search for help articles, guides, or ask a question..."
                  className="pl-12 pr-20 py-4 w-full text-base bg-[#1a1a1a] border border-gray-700 rounded-full text-white placeholder:text-gray-400 focus:border-purple-400 focus:outline-none"
                />
                <Button
                  type="submit"
                  className="absolute right-2 bg-purple-600 hover:bg-purple-700 text-white rounded-full px-6 py-2 transition-all duration-300 hover:scale-105"
                >
                  <Search className="w-4 h-4 mr-2" />
                  Search
                </Button>
              </div>
            </motion.form>
          </motion.div>

          {/* Support Stats */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
              {supportStats.map((stat, index) => {
                const Icon = stat.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="text-center"
                  >
                    <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2">
                      <div className="w-12 h-12 flex items-center justify-center bg-gradient-to-br from-purple-900/30 to-purple-800/20 rounded-full mb-4 mx-auto">
                        <Icon className={`h-6 w-6 ${stat.color}`} />
                      </div>
                      <h3 className="text-xl font-bold text-white mb-1">{stat.value}</h3>
                      <p className="text-gray-400 text-sm">{stat.label}</p>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Contact Methods */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Get in <span className="text-purple-400">Touch</span>
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Choose the support method that works best for you. Our team is ready to help!
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {contactMethods.map((method, index) => {
                const Icon = method.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="group cursor-pointer"
                    onClick={method.action}
                  >
                    <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 h-full">
                      <div className={`w-16 h-16 flex items-center justify-center bg-gradient-to-br ${method.color} rounded-full mb-6 mx-auto group-hover:scale-110 transition-transform duration-300`}>
                        <Icon className="h-8 w-8 text-white" />
                      </div>

                      <h3 className="text-xl font-semibold text-white mb-2 text-center">{method.method}</h3>
                      <p className="text-gray-400 text-center mb-4">{method.description}</p>

                      <div className="text-center">
                        <span className="bg-gray-800/50 text-purple-400 px-3 py-1 rounded-full text-sm">
                          {method.availability}
                        </span>
                      </div>

                      <div className="mt-4 text-center">
                        <Button
                          variant="ghost"
                          className="text-purple-400 hover:bg-purple-400/10 group-hover:text-purple-300"
                          onClick={method.action}
                        >
                          Contact Now
                          <ArrowRight className="ml-2 h-4 w-4 transition-transform group-hover:translate-x-1" />
                        </Button>
                      </div>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Help Resources */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Self-Help <span className="text-purple-400">Resources</span>
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Explore our comprehensive collection of guides, tutorials, and community resources
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {helpResources.map((resource, index) => {
                const Icon = resource.icon;
                return (
                  <motion.div
                    key={index}
                    variants={itemVariants}
                    className="group cursor-pointer"
                    onClick={resource.action}
                  >
                    <div className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-6 backdrop-blur-lg hover:border-purple-400/50 transition-all duration-300 hover:-translate-y-2 h-full">
                      <div className="flex items-center justify-between mb-4">
                        <Icon className={`h-8 w-8 ${resource.color}`} />
                        <span className="bg-gray-800/50 text-gray-400 px-2 py-1 rounded text-xs">
                          {resource.count}
                        </span>
                      </div>

                      <h3 className="text-lg font-semibold text-white mb-2">{resource.title}</h3>
                      <p className="text-gray-400 text-sm mb-4">{resource.description}</p>

                      <Button
                        variant="ghost"
                        className="text-purple-400 hover:bg-purple-400/10 w-full justify-between group"
                        onClick={resource.action}
                      >
                        Explore
                        <ArrowRight className="h-4 w-4 transition-transform group-hover:translate-x-1" />
                      </Button>
                    </div>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Quick FAQ */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="mb-16"
          >
            <motion.div variants={itemVariants} className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
                Quick <span className="text-purple-400">Answers</span>
              </h2>
              <p className="text-gray-400 max-w-2xl mx-auto">
                Find instant answers to the most common questions
              </p>
            </motion.div>

            <div className="max-w-4xl mx-auto space-y-4">
              {faqs.map((faq, index) => (
                <motion.div
                  key={index}
                  variants={itemVariants}
                  className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl backdrop-blur-lg overflow-hidden"
                >
                  <div
                    className="p-6 flex justify-between items-center cursor-pointer hover:bg-gray-800/50 transition-colors"
                    onClick={() => toggleExpand(index)}
                  >
                    <h3 className="font-semibold text-white pr-4">{faq.question}</h3>
                    <Button variant="ghost" size="icon" className="shrink-0">
                      {expanded === index ? (
                        <ChevronUp className="h-5 w-5 text-purple-400" />
                      ) : (
                        <ChevronDown className="h-5 w-5 text-gray-400" />
                      )}
                    </Button>
                  </div>

                  <AnimatePresence>
                    {expanded === index && (
                      <motion.div
                        initial={{ height: 0, opacity: 0 }}
                        animate={{ height: "auto", opacity: 1 }}
                        exit={{ height: 0, opacity: 0 }}
                        transition={{ duration: 0.3 }}
                        className="overflow-hidden"
                      >
                        <div className="p-6 pt-0 border-t border-gray-700/50">
                          <p className="text-gray-400">{faq.answer}</p>
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </motion.div>
              ))}
            </div>

            <motion.div variants={itemVariants} className="mt-8 text-center">
              <Button
                variant="outline"
                className="border-purple-400 text-purple-400 hover:bg-purple-400/10"
                onClick={() => toast.info('Loading more frequently asked questions')}
              >
                View All FAQs
                <ArrowRight className="ml-2 h-4 w-4" />
              </Button>
            </motion.div>
          </motion.div>

          {/* Contact Form */}
          <motion.div
            variants={containerVariants}
            initial="hidden"
            whileInView="visible"
            viewport={{ once: true }}
            className="max-w-2xl mx-auto"
          >
            <div className="bg-gradient-to-r from-purple-600/20 via-purple-500/10 to-purple-700/20 rounded-2xl p-8 border border-gray-700/50 backdrop-blur-lg relative overflow-hidden">
              <div className="absolute inset-0 bg-[url('/patterns/grid.svg')] opacity-5"></div>

              <div className="relative z-10">
                <motion.div variants={itemVariants} className="text-center mb-8">
                  <h2 className="text-2xl md:text-3xl font-bold text-white mb-4">
                    Still Need <span className="text-purple-400">Help?</span>
                  </h2>
                  <p className="text-gray-400">
                    Send us a message and our support team will get back to you within 2 hours
                  </p>
                </motion.div>

                <motion.form variants={itemVariants} onSubmit={handleContactSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-300 mb-2">
                        Your Name
                      </label>
                      <Input
                        id="name"
                        name="name"
                        value={contactForm.name}
                        onChange={handleInputChange}
                        placeholder="Enter your name"
                        className="bg-[#1a1a1a] border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-400"
                      />
                    </div>

                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-300 mb-2">
                        Email Address
                      </label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={contactForm.email}
                        onChange={handleInputChange}
                        placeholder="Enter your email"
                        className="bg-[#1a1a1a] border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-400"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-300 mb-2">
                      Your Message
                    </label>
                    <Textarea
                      id="message"
                      name="message"
                      value={contactForm.message}
                      onChange={handleInputChange}
                      placeholder="Describe your issue or question in detail..."
                      rows={5}
                      className="bg-[#1a1a1a] border-gray-600 text-white placeholder:text-gray-400 focus:border-purple-400 resize-none"
                    />
                  </div>

                  <Button
                    type="submit"
                    className="w-full bg-purple-600 hover:bg-purple-700 text-white py-3 transition-all duration-300 hover:scale-105"
                  >
                    <Mail className="w-4 h-4 mr-2" />
                    Send Message
                  </Button>
                </motion.form>
              </div>
            </div>
          </motion.div>
        </div>
      </div>
    </div>
  );
};

export default HelpCenter;
