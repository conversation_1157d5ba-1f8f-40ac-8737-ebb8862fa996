import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { motion } from "framer-motion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Chrome, Eye, EyeOff } from "lucide-react";
import { toast } from "sonner";
import { Link, useNavigate } from "react-router-dom";
import { validateSignInForm, FormErrors } from "@/lib/auth";

export const SimpleSignInForm = () => {
  const { signIn, signInWithProvider, loading } = useAuth();
  const navigate = useNavigate();
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form
    const formErrors = validateSignInForm(email, password);
    if (Object.keys(formErrors).length > 0) {
      setErrors(formErrors);
      return;
    }

    setIsSubmitting(true);
    setErrors({});

    try {
      const { error } = await signIn(email, password);

      if (!error) {
        navigate("/");
      } else {
        setErrors({ general: error.message });
      }
    } catch (err: any) {
      console.error("Sign in error:", err);
      setErrors({ general: "An unexpected error occurred" });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleGoogleSignIn = async () => {
    try {
      const { error } = await signInWithProvider('google');
      if (error) {
        toast.error(error.message);
      }
    } catch (err: any) {
      console.error("Google sign in error:", err);
      toast.error("Failed to sign in with Google");
    }
  };

  return (
    <div className="w-full max-w-md mx-auto">
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="bg-[#1a1a1a] border border-gray-700/50 rounded-xl p-8 backdrop-blur-lg space-y-6"
      >
        <div className="text-center">
          <h1 className="text-3xl font-semibold text-white mb-2">Welcome Back</h1>
          <p className="text-gray-400">Sign in to your Pegasus Tool account</p>
        </div>

        {/* Social Sign In Button */}
        <div className="space-y-3">
          <Button
            onClick={handleGoogleSignIn}
            variant="outline"
            className="w-full bg-[#1a1a1a] border border-gray-700/50 text-white hover:bg-gray-700/50 hover:border-purple-400/50 transition-all duration-300 backdrop-blur-lg"
            disabled={loading}
          >
            <Chrome className="w-4 h-4 mr-2" />
            Sign in with Google
          </Button>
        </div>

        {/* Divider */}
        <div className="relative">
          <div className="absolute inset-0 flex items-center">
            <span className="w-full border-t border-gray-700/50" />
          </div>
          <div className="relative flex justify-center text-xs uppercase">
            <span className="bg-[#1a1a1a] px-3 text-gray-400">OR</span>
          </div>
        </div>

        {/* Email/Password Form */}
        <form onSubmit={handleSubmit} className="space-y-5">
          <div className="space-y-2">
            <Label htmlFor="email" className="text-sm text-gray-300 font-medium">
              Email Address
            </Label>
            <Input
              id="email"
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="bg-[#111111] border border-gray-700/50 text-white placeholder:text-gray-500 focus:border-purple-400/50 focus:ring-purple-400/20 transition-all duration-300 h-12"
              placeholder="Enter your email"
              required
            />
            {errors.email && (
              <p className="text-red-400 text-sm">{errors.email}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="password" className="text-sm text-gray-300 font-medium">
              Password
            </Label>
            <div className="relative">
              <Input
                id="password"
                type={showPassword ? "text" : "password"}
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="bg-[#111111] border border-gray-700/50 text-white placeholder:text-gray-500 focus:border-purple-400/50 focus:ring-purple-400/20 transition-all duration-300 h-12 pr-12"
                placeholder="Enter your password"
                required
              />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-purple-400 transition-colors"
              >
                {showPassword ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
            </div>
            {errors.password && (
              <p className="text-red-400 text-sm">{errors.password}</p>
            )}
          </div>

          {errors.general && (
            <div className="bg-red-500/10 border border-red-500/20 rounded-lg p-3">
              <p className="text-red-400 text-sm text-center">{errors.general}</p>
            </div>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || loading || !email || !password}
            className="w-full bg-gradient-to-r from-purple-600 to-purple-500 hover:from-purple-700 hover:to-purple-600 text-white font-medium h-12 rounded-lg transition-all duration-300 hover:-translate-y-0.5 shadow-lg hover:shadow-purple-500/25"
          >
            {isSubmitting ? "Signing in..." : "Sign in to Pegasus Tool"}
          </Button>
        </form>

        {/* Forgot Password Link */}
        <div className="text-center">
          <Link
            to="/forgot-password"
            className="text-gray-400 hover:text-purple-400 text-sm hover:underline transition-colors"
          >
            Forgot your password?
          </Link>
        </div>

        {/* Sign Up Link */}
        <div className="text-center pt-2">
          <span className="text-gray-400 text-sm">
            Don't have an account?{" "}
            <Link
              to="/sign-up"
              className="text-purple-400 hover:text-purple-300 font-medium hover:underline transition-colors"
            >
              Create Account
            </Link>
          </span>
        </div>
      </motion.div>
    </div>
  );
};

export default SimpleSignInForm;
