import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import { getTheme, ThemeType } from '@/styles/design-system';

interface ThemedHeaderProps {
  theme?: ThemeType;
  title: string;
  subtitle?: string;
  highlightWord?: string;
  className?: string;
  size?: 'small' | 'normal' | 'large';
  align?: 'left' | 'center' | 'right';
  withIcon?: React.ReactNode;
  withUnderline?: boolean;
  animate?: boolean;
}

const ThemedHeader: React.FC<ThemedHeaderProps> = ({
  theme = 'neutral',
  title,
  subtitle,
  highlightWord,
  className,
  size = 'normal',
  align = 'center',
  withIcon,
  withUnderline = true,
  animate = true
}) => {
  const themeConfig = getTheme(theme);

  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return {
          title: 'text-2xl md:text-3xl',
          subtitle: 'text-base md:text-lg',
          spacing: 'mb-8'
        };
      case 'large':
        return {
          title: 'text-4xl md:text-5xl lg:text-6xl',
          subtitle: 'text-xl md:text-2xl',
          spacing: 'mb-16'
        };
      default:
        return {
          title: 'text-3xl md:text-4xl lg:text-5xl',
          subtitle: 'text-lg md:text-xl',
          spacing: 'mb-12'
        };
    }
  };

  const getAlignClass = () => {
    switch (align) {
      case 'left':
        return 'text-left';
      case 'right':
        return 'text-right';
      default:
        return 'text-center';
    }
  };

  const sizeClasses = getSizeClasses();

  const processTitle = () => {
    if (!highlightWord) return title;

    const parts = title.split(highlightWord);
    return (
      <>
        {parts[0]}
        <span className={cn(
          'bg-gradient-to-r bg-clip-text text-transparent font-bold',
          theme === 'hardware' ? 'from-pegasus-blue-600 to-pegasus-blue-400' :
          theme === 'software' ? 'from-orange-600 to-orange-400' :
          theme === 'purple' ? 'from-[#C084FC] to-purple-400' :
          'from-gray-600 to-gray-400'
        )}>
          {highlightWord}
        </span>
        {parts[1]}
      </>
    );
  };

  const headerContent = (
    <div className={cn(getAlignClass(), sizeClasses.spacing, className)}>
      {/* Icon */}
      {withIcon && (
        <motion.div
          className={cn(
            'inline-flex items-center justify-center w-16 h-16 rounded-2xl mb-6',
            theme === 'hardware' ? 'bg-pegasus-blue-100 dark:bg-pegasus-blue-900/30' :
            theme === 'software' ? 'bg-orange-100 dark:bg-orange-900/30' :
            theme === 'purple' ? 'bg-purple-100 dark:bg-purple-900/30' :
            'bg-gray-100 dark:bg-gray-800/30',
            themeConfig.primary
          )}
          initial={{ scale: 0, rotate: -180 }}
          whileInView={{ scale: 1, rotate: 0 }}
          transition={{ duration: 0.6, delay: 0.2 }}
          viewport={{ once: true }}
        >
          {withIcon}
        </motion.div>
      )}

      {/* Title */}
      <motion.h2
        className={cn(
          'font-bold tracking-tight leading-tight mb-4',
          sizeClasses.title,
          themeConfig.text
        )}
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: animate ? 0.1 : 0 }}
        viewport={{ once: true }}
      >
        {processTitle()}
      </motion.h2>

      {/* Subtitle */}
      {subtitle && (
        <motion.p
          className={cn(
            'max-w-3xl mx-auto leading-relaxed',
            sizeClasses.subtitle,
            themeConfig.secondary,
            align === 'left' && 'mx-0',
            align === 'right' && 'ml-auto mr-0'
          )}
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6, delay: animate ? 0.2 : 0 }}
          viewport={{ once: true }}
        >
          {subtitle}
        </motion.p>
      )}

      {/* Decorative Underline */}
      {withUnderline && (
        <motion.div
          className={cn(
            'h-1 rounded-full mt-6',
            align === 'center' ? 'w-24 mx-auto' :
            align === 'left' ? 'w-24' : 'w-24 ml-auto',
            theme === 'hardware' ? 'bg-gradient-to-r from-pegasus-blue-400 to-pegasus-blue-600' :
            theme === 'software' ? 'bg-gradient-to-r from-orange-400 to-orange-600' :
            theme === 'purple' ? 'bg-gradient-to-r from-[#C084FC] to-purple-600' :
            'bg-gradient-to-r from-gray-400 to-gray-600'
          )}
          initial={{ scaleX: 0 }}
          whileInView={{ scaleX: 1 }}
          transition={{ duration: 0.8, delay: animate ? 0.4 : 0 }}
          viewport={{ once: true }}
        />
      )}
    </div>
  );

  return headerContent;
};

export default ThemedHeader;
