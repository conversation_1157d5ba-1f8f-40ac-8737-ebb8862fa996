import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { User, LogIn } from "lucide-react";
import { Link } from "react-router-dom";
import { useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";

import { UserAvatar } from "./UserAvatar";

export const AuthButton = () => {
  const { user, loading } = useAuth();
  const location = useLocation();

  // Function to get button colors based on current page
  const getButtonColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      // Home page - Purple theme
      return {
        primary: 'bg-purple-500/10 hover:bg-purple-500/20 text-purple-400',
        secondary: 'text-purple-400 hover:text-purple-300'
      };
    } else if (currentPath === '/software') {
      // Software page - Orange theme
      return {
        primary: 'bg-orange-500/10 hover:bg-orange-500/20 text-orange-400',
        secondary: 'text-orange-400 hover:text-orange-300'
      };
    } else if (currentPath === '/hardware') {
      // Hardware page - Blue theme
      return {
        primary: 'bg-blue-500/10 hover:bg-blue-500/20 text-blue-400',
        secondary: 'text-blue-400 hover:text-blue-300'
      };
    } else {
      // Default - Purple theme
      return {
        primary: 'bg-purple-500/10 hover:bg-purple-500/20 text-purple-400',
        secondary: 'text-purple-400 hover:text-purple-300'
      };
    }
  };

  const buttonColors = getButtonColors();

  // Show loading state while auth is loading
  if (loading) {
    return (
      <div className="flex items-center gap-2">
        <div className="animate-pulse bg-gray-700 rounded-full h-9 w-9"></div>
      </div>
    );
  }

  return (
    <>
      {!user && (
        <div className="flex items-center gap-3">
          <Link to="/sign-in">
            <Button
              variant="ghost"
              size="sm"
              className={cn(
                "px-4 py-2 rounded-md transition-all duration-300",
                "backdrop-blur-sm border border-transparent",
                "hover:border-current/20",
                buttonColors.secondary
              )}
            >
              <LogIn className="w-4 h-4 mr-2" />
              Sign In
            </Button>
          </Link>
          <Link to="/sign-up">
            <Button
              size="sm"
              className={cn(
                "px-4 py-2 rounded-md transition-all duration-300",
                "backdrop-blur-sm border border-transparent",
                "hover:border-current/20",
                buttonColors.primary
              )}
            >
              <User className="w-4 h-4 mr-2" />
              Sign Up
            </Button>
          </Link>
        </div>
      )}
      {user && (
        <UserAvatar />
      )}
    </>
  );
};

export default AuthButton;
