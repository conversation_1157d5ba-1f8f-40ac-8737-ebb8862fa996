import React from 'react';
import { useNavigate, useLocation } from "react-router-dom";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Drawer, DrawerClose, DrawerContent, DrawerTrigger } from "@/components/ui/drawer";
import { X, Menu } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface MenuItem {
  title: string;
  href: string;
}

interface MobileNavProps {
  menuItems: MenuItem[];
}

const MobileNav: React.FC<MobileNavProps> = ({ menuItems }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [open, setOpen] = React.useState(false);

  // Function to get colors based on current page
  const getColors = () => {
    const currentPath = location.pathname;

    if (currentPath === '/') {
      return {
        text: 'text-purple-400',
        hover: 'hover:bg-purple-500/10',
        active: 'bg-purple-500/10 text-purple-400',
        border: 'border-purple-500/20'
      };
    } else if (currentPath === '/software') {
      return {
        text: 'text-orange-400',
        hover: 'hover:bg-orange-500/10',
        active: 'bg-orange-500/10 text-orange-400',
        border: 'border-orange-500/20'
      };
    } else if (currentPath === '/hardware') {
      return {
        text: 'text-blue-400',
        hover: 'hover:bg-blue-500/10',
        active: 'bg-blue-500/10 text-blue-400',
        border: 'border-blue-500/20'
      };
    } else {
      return {
        text: 'text-purple-400',
        hover: 'hover:bg-purple-500/10',
        active: 'bg-purple-500/10 text-purple-400',
        border: 'border-purple-500/20'
      };
    }
  };

  const colors = getColors();

  const handleNavigation = (href: string) => {
    setOpen(false);
    setTimeout(() => {
      if (href.startsWith('#')) {
        const element = document.getElementById(href.substring(1));
        if (element) {
          element.scrollIntoView({ behavior: 'smooth' });
          return;
        }
        if (location.pathname !== '/') {
          navigate('/', { state: { scrollTo: href.substring(1) } });
        }
      } else {
        navigate(href);
      }
    }, 300);
  };

  return (
    <div className="md:hidden">
      <Drawer open={open} onOpenChange={setOpen}>
        <DrawerTrigger asChild>
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "ml-2 transition-all duration-300",
              colors.text,
              "hover:bg-black/10 backdrop-blur-sm",
              open && "bg-black/10"
            )}
            aria-label="Toggle menu"
          >
            <Menu className="h-5 w-5" />
          </Button>
        </DrawerTrigger>
        <DrawerContent className="h-[80vh] p-0 bg-black/80 backdrop-blur-md">
          <div className="container mx-auto p-4">
            <div className="flex justify-end mb-4">
              <DrawerClose asChild>
                <Button 
                  variant="ghost" 
                  size="icon"
                  className={cn(
                    "transition-all duration-300",
                    colors.text,
                    "hover:bg-black/10"
                  )}
                >
                  <X className="h-5 w-5" />
                </Button>
              </DrawerClose>
            </div>
            <div className="flex flex-col space-y-2">
              <AnimatePresence mode="wait">
                {open && menuItems.map((item, index) => (
                  <motion.button
                    key={item.title}
                    onClick={() => handleNavigation(item.href)}
                    className={cn(
                      "py-3 px-4 font-medium text-sm transition-all duration-300 rounded-lg",
                      colors.text,
                      colors.hover,
                      (location.pathname === item.href ||
                       (location.pathname === '/' && item.href === '/')) &&
                      colors.active
                    )}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: index * 0.1 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    {item.title}
                  </motion.button>
                ))}
              </AnimatePresence>
            </div>
          </div>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default MobileNav;
