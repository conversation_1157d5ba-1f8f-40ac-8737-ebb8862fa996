import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';

interface StatisticsStyleHeaderProps {
  badge: string;
  title: string;
  highlightWord?: string;
  subtitle: string;
  className?: string;
}

const StatisticsStyleHeader: React.FC<StatisticsStyleHeaderProps> = ({
  badge,
  title,
  highlightWord,
  subtitle,
  className
}) => {
  const processTitle = () => {
    if (!highlightWord) return title;
    
    const parts = title.split(highlightWord);
    return (
      <>
        {parts[0]}
        <span className="text-purple-400">{highlightWord}</span>
        {parts[1]}
      </>
    );
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6 }}
      viewport={{ once: true }}
      className={cn("text-center mb-16", className)}
    >
      <div className="mb-6">
        <span className="bg-[#1a1a1a] border border-gray-700 text-purple-400 px-4 py-1 rounded-full text-xs sm:text-sm font-medium">
          {badge}
        </span>
      </div>
      <h2 className="text-3xl md:text-4xl lg:text-5xl font-semibold text-white leading-tight mb-4">
        {processTitle()}
      </h2>
      <p className="text-base sm:text-lg lg:text-xl text-gray-400 max-w-2xl mx-auto">
        {subtitle}
      </p>
    </motion.div>
  );
};

export default StatisticsStyleHeader;
